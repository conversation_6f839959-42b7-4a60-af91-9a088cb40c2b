package com.weinuo.quickcommands.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalDensity
import android.util.Log
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.data.SettingsRepository
import kotlin.math.PI
import kotlin.math.sin
import androidx.compose.runtime.collectAsState
import kotlinx.coroutines.delay
import kotlin.math.*
import kotlin.random.Random

/**
 * 弧线动画类型枚举
 */
enum class ArcAnimationType {
    NONE,            // 不显示弧线
    STATIC_PROGRESS, // 静态进度弧线（逐渐增长）
    ROTATING         // 旋转弧线（固定长度旋转）
}

/**
 * 粒子动画状态枚举
 *
 * 定义装饰性粒子的不同动画状态
 */
enum class ParticleAnimationState {
    /**
     * 正常状态 - 粒子在原位置轻微漂浮
     */
    NORMAL,

    /**
     * 收缩状态 - 粒子分层向水球中心移动并被吸入
     */
    CONTRACTING,

    /**
     * 处理状态 - 收缩完成后执行体检/优化操作，显示旋转弧线
     */
    PROCESSING,

    /**
     * 等待展示状态 - 处理完成后等待展示时长，显示旋转弧线
     */
    WAITING_DISPLAY,

    /**
     * 膨胀状态 - 粒子从水球中心螺旋爆发回到原位置
     */
    EXPANDING_STATIC,

    /**
     * 回弹状态 - 从膨胀状态平滑过渡回正常状态
     */
    BOUNCING_BACK,

    /**
     * 过渡状态 - 用于状态切换时的平滑过渡
     */
    TRANSITIONING
}

/**
 * 装饰性粒子数据类
 *
 * 用于表示水球周围的装饰性粒子
 */
data class DecorativeParticle(
    val id: Int,
    val originalAngle: Float,        // 原始角度（度）
    val originalDistance: Float,     // 原始距离（像素）
    val originalRadius: Float,       // 原始半径（像素）
    val currentAngle: Float,         // 当前角度
    val currentDistance: Float,      // 当前距离
    val currentRadius: Float,        // 当前半径
    val currentAlpha: Float,         // 当前透明度
    val layer: Int,                  // 所属层级（用于分层动画）
    val spiralOffset: Float = 0f,    // 螺旋偏移（用于爆发动画）
    val animationProgress: Float = 0f // 动画进度（0-1）
)

/**
 * 装饰性粒子动画管理器
 *
 * 负责管理装饰性粒子的状态、动画和渲染
 */
class DecorativeParticleManager {
    private var particles = mutableListOf<DecorativeParticle>()
    var currentState = ParticleAnimationState.NORMAL // 改为public以便外部访问
    private var animationStartTime = 0L
    private var stateChangeCallback: ((ParticleAnimationState) -> Unit)? = null
    private var contractionCompleteCallback: (() -> Unit)? = null

    companion object {
        // 动画时长配置
        private const val CONTRACTION_DURATION = 1200L // 收缩动画时长（毫秒）
        private const val EXPANSION_DURATION = 1500L   // 膨胀动画时长（毫秒）
        private const val LAYER_DELAY = 200L           // 层级间延迟（毫秒）

        // 粒子层级配置
        private const val TOTAL_LAYERS = 3             // 总层数

        // 动画参数
        private const val MIN_CONTRACTION_RADIUS = 0.5f // 收缩时最小半径倍数
        private const val MAX_EXPANSION_RADIUS = 1.2f   // 膨胀时最大半径倍数
        private const val SPIRAL_TURNS = 1.5f           // 螺旋圈数

        // 水球动画时长配置 - 与粒子动画同步
        const val WATER_BALL_CONTRACTION_DURATION = 1000L // 水球收缩动画时长
        const val WATER_BALL_EXPANSION_DURATION = 1200L   // 水球膨胀动画时长
        const val WATER_BALL_NORMAL_DURATION = 400L       // 回弹动画时长
    }

    /**
     * 初始化粒子
     */
    fun initializeParticles(waterBallRadius: Float, dpToPx: (Float) -> Float) {
        // 如果已经初始化过，不要重复初始化
        if (particles.isNotEmpty()) {
            println("DecorativeParticleManager: Particles already initialized, skipping")
            return
        }

        particles.clear()

        // 定义粒子的原始位置（与原DecorativeBubbles完全一致）
        val particleDefinitions = listOf(
            // 第一层（最内层）- 4个粒子
            Triple(45f, waterBallRadius + dpToPx(20f), dpToPx(3f)) to 0,
            Triple(135f, waterBallRadius + dpToPx(25f), dpToPx(2f)) to 0,
            Triple(225f, waterBallRadius + dpToPx(35f), dpToPx(2.3f)) to 0,
            Triple(315f, waterBallRadius + dpToPx(30f), dpToPx(1.5f)) to 0,

            // 第二层（中层）- 4个粒子
            Triple(75f, waterBallRadius + dpToPx(35f), dpToPx(2f)) to 1,
            Triple(165f, waterBallRadius + dpToPx(40f), dpToPx(1.8f)) to 1,
            Triple(195f, waterBallRadius + dpToPx(25f), dpToPx(1.7f)) to 1,
            Triple(345f, waterBallRadius + dpToPx(20f), dpToPx(2.8f)) to 1,

            // 第三层（最外层）- 2个粒子
            Triple(15f, waterBallRadius + dpToPx(45f), dpToPx(2.5f)) to 2,
            Triple(105f, waterBallRadius + dpToPx(30f), dpToPx(2.2f)) to 2
        )

        particleDefinitions.forEachIndexed { index, (definition, layer) ->
            val (angle, distance, radius) = definition
            particles.add(
                DecorativeParticle(
                    id = index,
                    originalAngle = angle,
                    originalDistance = distance,
                    originalRadius = radius,
                    currentAngle = angle,
                    currentDistance = distance,
                    currentRadius = radius,
                    currentAlpha = 1f,
                    layer = layer,
                    spiralOffset = Random.nextFloat() * 360f // 随机螺旋偏移
                )
            )
        }

        // 调试信息
        println("DecorativeParticleManager: Initialized ${particles.size} particles")

        // 不要重置状态，保持当前状态
        // currentState = ParticleAnimationState.NORMAL  // 注释掉这行，避免覆盖外部设置的状态
    }

    /**
     * 设置状态变化回调
     */
    fun setStateChangeCallback(callback: (ParticleAnimationState) -> Unit) {
        stateChangeCallback = callback
    }

    /**
     * 设置收缩动画完成回调
     */
    fun setContractionCompleteCallback(callback: () -> Unit) {
        contractionCompleteCallback = callback
    }

    /**
     * 开始收缩动画
     */
    fun startContraction() {
        println("DecorativeParticleManager: Starting contraction animation")
        currentState = ParticleAnimationState.CONTRACTING
        animationStartTime = System.currentTimeMillis()
    }

    /**
     * 开始膨胀动画
     */
    fun startExpansion() {
        println("DecorativeParticleManager: Starting expansion animation")
        currentState = ParticleAnimationState.EXPANDING_STATIC
        animationStartTime = System.currentTimeMillis()
    }

    /**
     * 开始回弹动画
     */
    fun startBouncingBack() {
        println("DecorativeParticleManager: Starting bouncing back animation")
        currentState = ParticleAnimationState.BOUNCING_BACK
        animationStartTime = System.currentTimeMillis()
    }

    /**
     * 重置到正常状态
     */
    fun resetToNormal() {
        println("DecorativeParticleManager: Resetting to normal state, current state was: $currentState")
        currentState = ParticleAnimationState.NORMAL
        animationStartTime = 0L
    }

    /**
     * 获取当前状态
     */
    fun getState(): ParticleAnimationState {
        println("DecorativeParticleManager: getState() returning: $currentState")
        return currentState
    }

    /**
     * 获取当前粒子列表
     */
    fun getParticles(): List<DecorativeParticle> = particles.toList()

    /**
     * 设置粒子列表
     */
    fun setParticles(newParticles: List<DecorativeParticle>) {
        particles.clear()
        particles.addAll(newParticles)
    }

    /**
     * 更新粒子状态
     */
    fun updateParticles(currentTime: Long, waveOffset: Float, waterBallCenterX: Float, waterBallCenterY: Float) {
        // 调试信息 - 每秒打印一次当前状态
        if (currentTime % 1000 < 50) {
            println("DecorativeParticleManager: Current state: $currentState")
        }

        when (currentState) {
            ParticleAnimationState.NORMAL -> updateNormalState(waveOffset)
            ParticleAnimationState.CONTRACTING -> updateContractionState(currentTime, waterBallCenterX, waterBallCenterY)
            ParticleAnimationState.PROCESSING -> updateProcessingState(waveOffset)
            ParticleAnimationState.WAITING_DISPLAY -> updateWaitingDisplayState(waveOffset)
            ParticleAnimationState.EXPANDING_STATIC -> updateExpansionState(currentTime, waterBallCenterX, waterBallCenterY)
            ParticleAnimationState.BOUNCING_BACK -> updateBouncingBackState(currentTime, waveOffset)
            ParticleAnimationState.TRANSITIONING -> { /* 暂时不处理 */ }
        }
    }

    /**
     * 更新正常状态的粒子（轻微漂浮）
     */
    private fun updateNormalState(waveOffset: Float) {
        particles.forEachIndexed { index, particle ->
            val floatOffset = sin(waveOffset + index * 0.8f) * 5f
            particles[index] = particle.copy(
                currentDistance = particle.originalDistance + floatOffset,
                currentAlpha = 1f,
                currentRadius = particle.originalRadius,
                animationProgress = 0f
            )
        }
    }

    /**
     * 更新处理状态的粒子（保持收缩后的位置，不颤抖）
     */
    private fun updateProcessingState(waveOffset: Float) {
        particles.forEachIndexed { index, particle ->
            // 处理状态：粒子保持在收缩后的位置，不做任何动画
            // 粒子应该已经在收缩完成后处于正确的位置（靠近中心）
            particles[index] = particle.copy(
                currentDistance = 0f, // 保持在中心位置
                currentAlpha = 0f,    // 保持隐藏状态
                currentRadius = particle.originalRadius * 0.5f, // 保持较小的半径
                animationProgress = 1f // 保持收缩完成状态
            )
        }
    }

    /**
     * 更新等待展示状态的粒子（保持收缩后的位置，不颤抖）
     */
    private fun updateWaitingDisplayState(waveOffset: Float) {
        particles.forEachIndexed { index, particle ->
            // 等待展示状态：粒子保持在收缩后的位置，不做任何动画
            // 粒子应该已经在收缩完成后处于正确的位置（靠近中心）
            particles[index] = particle.copy(
                currentDistance = 0f, // 保持在中心位置
                currentAlpha = 0f,    // 保持隐藏状态
                currentRadius = particle.originalRadius * 0.5f, // 保持较小的半径
                animationProgress = 1f // 保持收缩完成状态
            )
        }
    }

    /**
     * 更新收缩状态的粒子（分层向中心移动）
     */
    private fun updateContractionState(currentTime: Long, centerX: Float, centerY: Float) {
        val elapsed = currentTime - animationStartTime

        // 调试信息
        if (elapsed % 500 < 50) { // 每500ms打印一次，避免过多日志
            println("DecorativeParticleManager: Contraction elapsed: ${elapsed}ms")
        }

        particles.forEachIndexed { index, particle ->
            // 计算该层级的动画开始时间
            val layerStartTime = particle.layer * LAYER_DELAY
            val layerElapsed = maxOf(0L, elapsed - layerStartTime)
            val layerProgress = minOf(1f, layerElapsed.toFloat() / CONTRACTION_DURATION)

            if (layerProgress > 0f) {
                // 使用EaseInOutCubic缓动函数
                val easedProgress = easeInOutCubic(layerProgress)

                // 计算目标位置（水球中心）
                val targetDistance = 0f
                val targetRadius = particle.originalRadius * MIN_CONTRACTION_RADIUS
                val targetAlpha = if (easedProgress > 0.8f) {
                    // 接近中心时开始淡出
                    1f - (easedProgress - 0.8f) / 0.2f
                } else {
                    1f
                }

                particles[index] = particle.copy(
                    currentDistance = lerp(particle.originalDistance, targetDistance, easedProgress),
                    currentRadius = lerp(particle.originalRadius, targetRadius, easedProgress),
                    currentAlpha = targetAlpha,
                    animationProgress = layerProgress
                )
            }
        }

        // 检查是否所有粒子都完成了收缩动画
        if (elapsed >= CONTRACTION_DURATION + (TOTAL_LAYERS - 1) * LAYER_DELAY) {
            // 收缩动画完成，通知外部
            contractionCompleteCallback?.invoke()
        }
    }

    /**
     * 更新膨胀状态的粒子（螺旋爆发回原位置）
     */
    private fun updateExpansionState(currentTime: Long, centerX: Float, centerY: Float) {
        val elapsed = currentTime - animationStartTime
        val progress = minOf(1f, elapsed.toFloat() / EXPANSION_DURATION)
        val easedProgress = easeOutBack(progress)

        particles.forEachIndexed { index, particle ->
            // 优化的螺旋角度计算 - 直接顺时针飞到目标位置，不飞过头
            val maxSpiralAngle = particle.originalAngle + particle.spiralOffset * 0.5f // 减少螺旋幅度
            val currentAngle = if (easedProgress < 0.6f) {
                // 前60%：从中心螺旋飞出
                lerp(0f, maxSpiralAngle, easedProgress / 0.6f)
            } else {
                // 后40%：直接飞到目标位置
                lerp(maxSpiralAngle, particle.originalAngle, (easedProgress - 0.6f) / 0.4f)
            }

            // 距离动画 - 确保最终回到原始距离
            val maxDistance = particle.originalDistance * MAX_EXPANSION_RADIUS
            val currentDistance = if (easedProgress < 0.3f) {
                // 前30%：从中心快速膨胀
                lerp(0f, maxDistance, easedProgress / 0.3f)
            } else {
                // 后70%：回弹到原位置
                lerp(maxDistance, particle.originalDistance, (easedProgress - 0.3f) / 0.7f)
            }

            // 大小动画 - 确保最终回到原始大小
            val currentRadius = if (easedProgress < 0.2f) {
                // 前20%：粒子放大
                lerp(MIN_CONTRACTION_RADIUS * particle.originalRadius, particle.originalRadius * 1.3f, easedProgress / 0.2f)
            } else {
                // 后80%：回到原大小
                lerp(particle.originalRadius * 1.3f, particle.originalRadius, (easedProgress - 0.2f) / 0.8f)
            }

            val currentAlpha = if (easedProgress < 0.1f) {
                // 前10%：淡入
                easedProgress / 0.1f
            } else {
                1f
            }

            particles[index] = particle.copy(
                currentAngle = currentAngle,
                currentDistance = currentDistance,
                currentRadius = currentRadius,
                currentAlpha = currentAlpha,
                animationProgress = progress
            )
        }

        // 检查是否完成膨胀动画
        if (progress >= 1f) {
            // 确保所有粒子都回到原始位置
            particles.forEachIndexed { index, particle ->
                particles[index] = particle.copy(
                    currentAngle = particle.originalAngle,
                    currentDistance = particle.originalDistance,
                    currentRadius = particle.originalRadius,
                    currentAlpha = 1f,
                    animationProgress = 0f
                )
            }
            // 膨胀完成后进入回弹状态，而不是直接跳到正常状态
            currentState = ParticleAnimationState.BOUNCING_BACK
            animationStartTime = System.currentTimeMillis() // 重置动画开始时间
            stateChangeCallback?.invoke(currentState)
        }
    }

    /**
     * 更新回弹状态的粒子（从膨胀状态平滑过渡到正常状态）
     */
    private fun updateBouncingBackState(currentTime: Long, waveOffset: Float) {
        val elapsed = currentTime - animationStartTime
        val progress = minOf(1f, elapsed.toFloat() / WATER_BALL_NORMAL_DURATION)
        val easedProgress = easeOutCubic(progress)

        particles.forEachIndexed { index, particle ->
            // 从膨胀状态的轻微放大平滑回到正常状态
            val scaleProgress = 1f - easedProgress * 0.05f // 从1.05倍缩放回1.0倍
            val floatOffset = sin(waveOffset + index * 0.8f) * 5f * easedProgress // 逐渐恢复漂浮效果

            particles[index] = particle.copy(
                currentDistance = particle.originalDistance + floatOffset,
                currentAlpha = 1f,
                currentRadius = particle.originalRadius * scaleProgress,
                animationProgress = progress
            )
        }

        // 检查是否完成回弹动画
        if (progress >= 1f) {
            // 确保所有粒子都回到正常状态
            particles.forEachIndexed { index, particle ->
                particles[index] = particle.copy(
                    currentAngle = particle.originalAngle,
                    currentDistance = particle.originalDistance,
                    currentRadius = particle.originalRadius,
                    currentAlpha = 1f,
                    animationProgress = 0f
                )
            }
            currentState = ParticleAnimationState.NORMAL
            stateChangeCallback?.invoke(currentState)
        }
    }

    /**
     * 缓动函数：EaseInOutCubic
     */
    private fun easeInOutCubic(t: Float): Float {
        return if (t < 0.5f) {
            4f * t * t * t
        } else {
            1f - (-2f * t + 2f).pow(3) / 2f
        }
    }

    /**
     * 缓动函数：EaseOutBack（回弹效果）
     */
    private fun easeOutBack(t: Float): Float {
        val c1 = 1.70158f
        val c3 = c1 + 1f
        return 1f + c3 * (t - 1f).pow(3) + c1 * (t - 1f).pow(2)
    }

    /**
     * 缓动函数：EaseOutCubic
     */
    private fun easeOutCubic(t: Float): Float {
        return 1f - (1f - t).pow(3)
    }

    /**
     * 线性插值函数
     */
    private fun lerp(start: Float, end: Float, fraction: Float): Float {
        return start + fraction * (end - start)
    }
}

/**
 * 内部气泡数据类
 *
 * 用于表示水球内部上升的气泡动画
 */
data class InternalBubble(
    val id: Int,
    val startX: Float,
    val currentX: Float, // 当前X位置
    val currentY: Float,
    val targetY: Float,
    val radius: Float,
    val speed: Float,
    val horizontalDrift: Float, // 水平漂移速度
    val alpha: Float,
    val creationTime: Long,
    val swayOffset: Float = 0f // 左右摆动偏移
)

/**
 * 内部气泡管理器
 *
 * 负责管理水球内部气泡的生成、更新和移除
 */
class InternalBubbleManager {
    private val bubbles = mutableListOf<InternalBubble>()
    private var nextBubbleId = 0
    private var lastGenerationTime = 0L
    private var bubblesInCurrentGroup = 0 // 当前组中已生成的气泡数量
    private var isInPause = false // 是否在停顿期间
    private var pauseStartTime = 0L // 停顿开始时间

    companion object {
        private const val GENERATION_INTERVAL = 350L // 组内气泡生成间隔（毫秒）- 放慢速度
        private const val BUBBLES_PER_GROUP = 9 // 每组气泡数量 - 改为9个
        private const val GROUP_PAUSE_DURATION = 600L // 组间停顿时间（毫秒）- 缩短等待时间
        private const val MIN_RADIUS = 1.5f // 最小气泡半径（dp）
        private const val MAX_RADIUS = 4.0f // 最大气泡半径（dp）
        private const val MIN_SPEED = 250f // 最小上升速度（dp/秒）
        private const val MAX_SPEED = 350f // 最大上升速度（dp/秒）
        private const val MIN_HORIZONTAL_DRIFT = -60f // 最小水平漂移速度（dp/秒）- 增加角度变化
        private const val MAX_HORIZONTAL_DRIFT = 60f // 最大水平漂移速度（dp/秒）- 增加角度变化
        private const val SWAY_AMPLITUDE = 8f // 左右摆动幅度（dp）
    }

    /**
     * 更新气泡状态
     *
     * @param currentTime 当前时间戳
     * @param canvasSize Canvas尺寸
     * @param waterLevel 当前水位（0-1）
     * @param deltaTime 时间增量（秒）
     */
    fun updateBubbles(
        currentTime: Long,
        canvasSize: androidx.compose.ui.geometry.Size,
        waterLevel: Float,
        deltaTime: Float
    ) {
        // 移除离开水球区域的气泡（满水状态下简化逻辑）
        bubbles.removeAll { bubble ->
            // 计算水球的实际顶部位置
            val centerY = canvasSize.height / 2f
            val waterBallRadius = minOf(canvasSize.width, canvasSize.height) / 2f - 8f
            val waterBallTop = centerY - waterBallRadius

            // 气泡到达水球顶部就直接移除
            bubble.currentY <= waterBallTop
        }

        // 生成新气泡 - 组生成模式（4个一组，组间停顿）
        if (isInPause) {
            // 检查停顿是否结束
            if (currentTime - pauseStartTime > GROUP_PAUSE_DURATION) {
                isInPause = false
                bubblesInCurrentGroup = 0
            }
        } else {
            // 正常生成模式
            if (currentTime - lastGenerationTime > GENERATION_INTERVAL) {
                generateNewBubble(currentTime, canvasSize, waterLevel)
                lastGenerationTime = currentTime
                bubblesInCurrentGroup++

                // 检查是否完成一组
                if (bubblesInCurrentGroup >= BUBBLES_PER_GROUP) {
                    isInPause = true
                    pauseStartTime = currentTime
                }
            }
        }

        // 更新现有气泡位置（满水状态下的简化逻辑）
        for (i in bubbles.indices) {
            val bubble = bubbles[i]
            val newY = bubble.currentY - bubble.speed * deltaTime
            val newX = bubble.currentX + bubble.horizontalDrift * deltaTime

            // 计算左右摆动
            val swayX = sin((currentTime - bubble.creationTime) / 1000f * 2f) * SWAY_AMPLITUDE

            bubbles[i] = bubble.copy(
                currentX = newX,
                currentY = newY,
                swayOffset = swayX,
                alpha = 1f // 满水状态下气泡始终保持完全不透明
            )
        }
    }

    /**
     * 生成新气泡
     */
    private fun generateNewBubble(currentTime: Long, canvasSize: androidx.compose.ui.geometry.Size, waterLevel: Float) {
        val radius = Random.nextFloat() * (MAX_RADIUS - MIN_RADIUS) + MIN_RADIUS
        val speed = Random.nextFloat() * (MAX_SPEED - MIN_SPEED) + MIN_SPEED

        // 大幅增加水平漂移角度 - 让气泡明显分散
        val horizontalDrift = if (Random.nextFloat() < 0.2f) {
            // 20%概率生成小漂移（往中间）
            (Random.nextFloat() - 0.5f) * 30f
        } else {
            // 80%概率生成大漂移（往两边）
            val direction = if (Random.nextBoolean()) 1f else -1f
            direction * (80f + Random.nextFloat() * 70f) // 80-150的大漂移速度
        }

        // 计算水球的实际位置和大小
        val centerX = canvasSize.width / 2f
        val centerY = canvasSize.height / 2f
        val waterBallRadius = minOf(canvasSize.width, canvasSize.height) / 2f - 8f // 与drawStaticWaterBall保持一致

        // 在水球中下方区域生成气泡
        val generationAreaWidth = waterBallRadius * 0.6f // 生成区域宽度，集中在中央
        val startX = centerX + (Random.nextFloat() - 0.5f) * generationAreaWidth

        // 从水球中心下方40%半径的位置开始生成，增加垂直随机性
        val baseY = centerY + waterBallRadius * 0.4f
        val verticalVariation = waterBallRadius * 0.1f // 垂直方向10%的随机变化
        val startY = baseY + (Random.nextFloat() - 0.5f) * verticalVariation
        val targetY = centerY - waterBallRadius // 目标是水球顶部

        val bubble = InternalBubble(
            id = nextBubbleId++,
            startX = startX,
            currentX = startX, // 初始X位置
            currentY = startY,
            targetY = targetY,
            radius = radius,
            speed = speed,
            horizontalDrift = horizontalDrift, // 水平漂移速度
            alpha = 1f,
            creationTime = currentTime
        )

        bubbles.add(bubble)
    }

    /**
     * 获取当前所有气泡
     */
    fun getBubbles(): List<InternalBubble> = bubbles.toList()

    /**
     * 清除所有气泡
     */
    fun clearBubbles() {
        bubbles.clear()
    }
}

/**
 * 主题感知的水球组件
 *
 * 特点：
 * - 圆形水球，根据分数显示水位高度
 * - 动态波浪效果，持续荡漾
 * - 根据分数和主题变化颜色：
 *   * 优秀(80-100)：使用主题品牌色
 *   * 良好(60-79)：海洋蓝主题使用MD3标准橙色，天空蓝主题使用iOS风格橙色
 *   * 需要优化(0-59)：使用主题错误色
 * - 分数文字居中显示，"分"字在右下角
 * - 装饰性气泡效果
 * - 进入时的灌水动画
 * - 支持静态进度弧线和旋转弧线两种模式
 * - 可选的内部气泡动画效果
 */
@Composable
fun WaterBallComponent(
    score: Int,
    modifier: Modifier = Modifier,
    size: Dp = 200.dp,
    isAnimating: Boolean = true,
    arcType: ArcAnimationType = ArcAnimationType.STATIC_PROGRESS,
    showInternalBubbles: Boolean = false,
    particleAnimationState: ParticleAnimationState = ParticleAnimationState.NORMAL,
    isProcessing: Boolean = false, // 新增：是否正在处理（体检/优化）
    showGrid: Boolean = false, // 控制是否显示网格，默认不显示
    motionSensingEnabled: Boolean = false, // 新增：是否启用体感跟随
    onParticleAnimationStateChange: (ParticleAnimationState) -> Unit = {}
) {
    // 分数动画 - 进入时从0逐渐增加到目标分数
    val animatedScore by animateFloatAsState(
        targetValue = score / 100f,
        animationSpec = tween(
            durationMillis = 2000,
            easing = EaseOutCubic
        ),
        label = "score_animation"
    )
    
    // 波浪偏移动画 - 持续的水面荡漾效果
    val infiniteTransition = rememberInfiniteTransition(label = "wave_animation")
    val waveOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(9000, easing = LinearEasing), // 调整到9秒
            repeatMode = RepeatMode.Restart
        ),
        label = "wave_offset"
    )

    // 弧线动画进度（从0度逐渐增长到固定角度）- 仅用于静态进度弧线
    val arcAnimatable = remember { Animatable(0f) }

    // 旋转弧线动画 - 仅用于旋转弧线
    val arcRotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "arc_rotation"
    )

    // 水球大小动画 - 根据粒子动画状态调整大小，时序与粒子动画同步
    val waterBallScale by animateFloatAsState(
        targetValue = when (particleAnimationState) {
            ParticleAnimationState.CONTRACTING -> 0.9f  // 收缩时缩小到0.9倍
            ParticleAnimationState.PROCESSING -> 0.9f   // 处理时保持收缩状态的大小
            ParticleAnimationState.WAITING_DISPLAY -> 0.9f  // 等待展示时保持收缩状态的大小
            ParticleAnimationState.EXPANDING_STATIC -> 1.05f   // 膨胀时放大到1.05倍
            ParticleAnimationState.BOUNCING_BACK -> 1.0f       // 回弹时回到正常大小
            else -> 1.0f                                 // 正常状态为1.0倍
        },
        animationSpec = tween(
            durationMillis = when (particleAnimationState) {
                ParticleAnimationState.CONTRACTING -> DecorativeParticleManager.WATER_BALL_CONTRACTION_DURATION.toInt()
                ParticleAnimationState.PROCESSING -> 0 // 处理时不需要动画，保持当前大小
                ParticleAnimationState.WAITING_DISPLAY -> 0 // 等待展示时不需要动画，保持当前大小
                ParticleAnimationState.EXPANDING_STATIC -> DecorativeParticleManager.WATER_BALL_EXPANSION_DURATION.toInt()
                ParticleAnimationState.BOUNCING_BACK -> DecorativeParticleManager.WATER_BALL_NORMAL_DURATION.toInt()
                else -> DecorativeParticleManager.WATER_BALL_NORMAL_DURATION.toInt()
            },
            easing = when (particleAnimationState) {
                ParticleAnimationState.CONTRACTING -> EaseInOutCubic
                ParticleAnimationState.PROCESSING -> EaseOutCubic
                ParticleAnimationState.WAITING_DISPLAY -> EaseOutCubic
                ParticleAnimationState.EXPANDING_STATIC -> EaseOutBack
                ParticleAnimationState.BOUNCING_BACK -> EaseOutCubic
                else -> EaseOutCubic
            }
        ),
        label = "water_ball_scale"
    )

    // 水球呼吸动画 - 仅在体检/优化期间启用
    val breathingScale by infiniteTransition.animateFloat(
        initialValue = 1.0f,
        targetValue = 1.02f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "breathing_scale"
    )

    // 涟漪效果动画 - 仅在体检/优化期间启用
    val rippleOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "ripple_offset"
    )

    // 计算最终的水球缩放比例（结合大小动画和呼吸动画）
    // 只在处理过程中应用呼吸效果（体检/优化过程中的颤抖）
    val finalWaterBallScale = if (isProcessing) {
        waterBallScale * breathingScale  // 处理期间应用呼吸效果（颤抖）
    } else {
        waterBallScale  // 非处理状态不应用呼吸效果，不颤抖
    }

    // 光晕效果动画 - 仅在膨胀状态时显示
    val glowAnimatable = remember { Animatable(0f) }

    // 监听膨胀状态，触发光晕动画
    LaunchedEffect(particleAnimationState) {
        if (particleAnimationState == ParticleAnimationState.EXPANDING_STATIC) {
            // 开始光晕动画
            glowAnimatable.animateTo(
                targetValue = 1f,
                animationSpec = tween(durationMillis = 800, easing = EaseOutCubic)
            )
            // 然后淡出
            glowAnimatable.animateTo(
                targetValue = 0f,
                animationSpec = tween(durationMillis = 1000, easing = EaseInCubic)
            )
        } else {
            // 其他状态立即重置光晕
            glowAnimatable.snapTo(0f)
        }
    }

    // 装饰性粒子管理器
    val particleManager = remember { DecorativeParticleManager() }
    val density = LocalDensity.current

    // 体感跟随管理器
    val motionContext = LocalContext.current
    val motionSensingManager = remember { MotionSensingManager.getInstance(motionContext) }

    // 监听体感跟随开关状态
    LaunchedEffect(motionSensingEnabled) {
        Log.d("WaterBall", "Motion sensing enabled changed: $motionSensingEnabled")
        if (motionSensingEnabled) {
            motionSensingManager.startSensing()
        } else {
            motionSensingManager.stopSensing()
        }
    }

    // 组件销毁时停止体感监听
    DisposableEffect(Unit) {
        onDispose {
            motionSensingManager.stopSensing()
        }
    }

    // 不设置自动状态切换回调，完全由ViewModel控制状态

    // 初始化粒子位置 - 只在第一次或size改变时初始化
    LaunchedEffect(size) {
        with(density) {
            if (particleManager.getParticles().isEmpty()) {
                particleManager.initializeParticles(
                    waterBallRadius = size.toPx() / 2f,
                    dpToPx = { dpValue -> dpValue.dp.toPx() }
                )

                // 初始化完成后，立即应用当前的动画状态
                println("WaterBallComponent: Particles initialized, applying current state: $particleAnimationState")
                when (particleAnimationState) {
                    ParticleAnimationState.CONTRACTING -> {
                        particleManager.startContraction()
                    }
                    ParticleAnimationState.PROCESSING -> {
                        // 处理状态：直接设置粒子为隐藏状态（首次体检时的情况）
                        println("WaterBallComponent: Setting particles to hidden state for PROCESSING")
                        val currentParticles = particleManager.getParticles().toMutableList()
                        currentParticles.forEachIndexed { index, particle ->
                            currentParticles[index] = particle.copy(
                                currentDistance = 0f,
                                currentAlpha = 0f,
                                currentRadius = particle.originalRadius * 0.5f,
                                animationProgress = 1f
                            )
                        }
                        particleManager.setParticles(currentParticles)
                        particleManager.currentState = ParticleAnimationState.PROCESSING
                    }
                    ParticleAnimationState.EXPANDING_STATIC -> {
                        particleManager.startExpansion()
                    }
                    ParticleAnimationState.NORMAL -> {
                        particleManager.resetToNormal()
                    }
                    else -> { /* 其他状态不需要特殊处理 */ }
                }
            }
        }
    }

    // 监听粒子动画状态变化 - 确保在粒子初始化后才处理状态变化
    LaunchedEffect(particleAnimationState) {
        if (particleManager.getParticles().isNotEmpty()) {
            println("WaterBallComponent: Particle animation state changed to $particleAnimationState")
            when (particleAnimationState) {
                ParticleAnimationState.CONTRACTING -> {
                    particleManager.startContraction()
                }
                ParticleAnimationState.PROCESSING -> {
                    // 处理状态：直接设置粒子为隐藏状态（首次体检时的情况）
                    println("WaterBallComponent: Setting particles to hidden state for PROCESSING")
                    val currentParticles = particleManager.getParticles().toMutableList()
                    currentParticles.forEachIndexed { index, particle ->
                        currentParticles[index] = particle.copy(
                            currentDistance = 0f,
                            currentAlpha = 0f,
                            currentRadius = particle.originalRadius * 0.5f,
                            animationProgress = 1f
                        )
                    }
                    particleManager.setParticles(currentParticles)
                    particleManager.currentState = ParticleAnimationState.PROCESSING
                }
                ParticleAnimationState.WAITING_DISPLAY -> {
                    // 等待展示状态：保持收缩后的状态，不做额外处理
                    // 粒子应该已经在收缩完成后处于正确位置
                }
                ParticleAnimationState.EXPANDING_STATIC -> {
                    particleManager.startExpansion()
                }
                ParticleAnimationState.BOUNCING_BACK -> {
                    particleManager.startBouncingBack()
                }
                ParticleAnimationState.NORMAL -> {
                    // 只有在当前不是NORMAL状态时才重置
                    if (particleManager.getState() != ParticleAnimationState.NORMAL) {
                        particleManager.resetToNormal()
                    }
                }
                else -> { /* 其他状态不需要特殊处理 */ }
            }
        } else {
            println("WaterBallComponent: Particles not initialized yet, state change ignored: $particleAnimationState")
        }
    }

    // 内部气泡管理器 - 仅在启用内部气泡时创建
    val bubbleManager = remember { if (showInternalBubbles) InternalBubbleManager() else null }
    var lastBubbleUpdateTime by remember { mutableLongStateOf(System.currentTimeMillis()) }

    LaunchedEffect(arcType) {
        if (arcType == ArcAnimationType.STATIC_PROGRESS) {
            arcAnimatable.animateTo(
                targetValue = 1f,
                animationSpec = tween(durationMillis = 2000, easing = EaseOutCubic) // 与水位动画同步
            )
        } else {
            arcAnimatable.snapTo(0f)
        }
    }
    
    // 获取主题上下文和设置
    val themeContext = LocalThemeContext.current
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 判断是否启用高级材质效果（仅在天空蓝主题下有效）
    val isAdvancedMaterialEnabled = remember(globalSettings.waterBallAdvancedMaterialEnabled, themeContext.theme.id) {
        globalSettings.waterBallAdvancedMaterialEnabled && themeContext.theme.id == "sky_blue"
    }

    // 判断是否启用水波动效预留空间（仅在天空蓝主题下有效）
    val isWaveReserveSpaceEnabled = remember(globalSettings.waterWaveReserveSpaceEnabled, themeContext.theme.id) {
        val enabled = globalSettings.waterWaveReserveSpaceEnabled && themeContext.theme.id == "sky_blue"
        Log.d("WaterBall", "Wave reserve space enabled: $enabled (setting: ${globalSettings.waterWaveReserveSpaceEnabled}, theme: ${themeContext.theme.id})")
        enabled
    }

    // 获取水波动效阈值设置
    val waveStopThreshold = remember(globalSettings.waterWaveStopThreshold) {
        globalSettings.waterWaveStopThreshold
    }

    // 根据分数和主题确定水球颜色
    val waterColor = remember(score, isAnimating, themeContext) {
        when {
            // 数据加载中时使用品牌色，避免初始0分显示红色的误导
            !isAnimating -> themeContext.colorScheme.primary // 品牌色 - 加载中
            score >= 80 -> themeContext.colorScheme.primary  // 品牌色 - 优秀
            score >= 60 -> {
                // 良好：根据主题风格选择橙色
                when (themeContext.theme.id) {
                    "ocean_blue" -> Color(0xFFFF9800) // MD3标准橙色（保持现有）
                    "sky_blue" -> Color(0xFFFF8C00)   // iOS风格橙色（更鲜艳）
                    else -> Color(0xFFFF9800)         // 默认MD3橙色
                }
            }
            else -> themeContext.colorScheme.error           // 错误色 - 需要优化
        }
    }

    // 获取高级材质颜色方案（仅在启用高级材质时使用）
    val advancedColorSet = remember(score, isAdvancedMaterialEnabled) {
        if (isAdvancedMaterialEnabled) {
            WaterBallAdvancedColorSchemes.getColorSetForScore(score)
        } else {
            null
        }
    }



    // 使用更大的容器来容纳水球和周围的气泡
    Box(
        modifier = modifier.size(size * 1.4f), // 扩大容器以容纳周围气泡
        contentAlignment = Alignment.Center
    ) {
        // 装饰性粒子效果 - 放在水球后面
        EnhancedDecorativeParticles(
            modifier = Modifier.fillMaxSize(),
            particleManager = particleManager,
            bubbleColor = waterColor.copy(alpha = 0.3f),
            waveOffset = waveOffset,
            waterBallSize = size
        )

        // 弧线动画 - 根据类型显示不同的弧线动画
        if (isAnimating) {
            // 确定弧线颜色：高级材质模式下使用高级颜色方案，否则使用水球颜色
            val arcColor = if (advancedColorSet != null) {
                advancedColorSet.gradientEnd.copy(alpha = if (arcType == ArcAnimationType.ROTATING) 0.9f else 0.8f)
            } else {
                waterColor.copy(alpha = if (arcType == ArcAnimationType.ROTATING) 0.9f else 0.8f)
            }

            when (arcType) {
                ArcAnimationType.NONE -> {
                    // 不显示弧线
                }
                ArcAnimationType.STATIC_PROGRESS -> {
                    StaticProgressArcAnimation(
                        modifier = Modifier.fillMaxSize(),
                        arcColor = arcColor,
                        progress = animatedScore, // 使用与水位同步的进度
                        arcAnimationProgress = arcAnimatable.value, // 传递弧线动画进度
                        waterBallSize = size
                    )
                }
                ArcAnimationType.ROTATING -> {
                    ProcessingArcAnimation(
                        modifier = Modifier.fillMaxSize(),
                        arcColor = arcColor,
                        rotation = arcRotation,
                        waterBallSize = size,
                        waterBallScale = finalWaterBallScale
                    )
                }
            }
        }

        // 水球主体容器 - 应用大小动画和呼吸效果
        Box(
            modifier = Modifier.size(size * finalWaterBallScale),
            contentAlignment = Alignment.Center
        ) {
            // 光晕效果 - 在水球后面绘制
            if (glowAnimatable.value > 0f) {
                Canvas(
                    modifier = Modifier.fillMaxSize()
                ) {
                    val center = Offset(this.size.width / 2f, this.size.height / 2f)
                    val baseRadius = minOf(this.size.width, this.size.height) / 2f
                    val glowAlpha = glowAnimatable.value

                    // 绘制多层光晕，从内到外逐渐扩散
                    for (i in 1..4) {
                        val glowRadius = baseRadius + (i * 15f * glowAlpha)
                        val layerAlpha = glowAlpha * (0.3f / i) // 外层更透明

                        drawCircle(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    waterColor.copy(alpha = layerAlpha),
                                    Color.Transparent
                                ),
                                center = center,
                                radius = glowRadius
                            ),
                            radius = glowRadius,
                            center = center
                        )
                    }
                }
            }

            // 水球Canvas绘制
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .clipToBounds()
            ) {
                // 更新内部气泡状态（如果启用）
                val internalBubbles = if (showInternalBubbles && bubbleManager != null) {
                    val currentTime = System.currentTimeMillis()
                    val deltaTime = (currentTime - lastBubbleUpdateTime) / 1000f
                    bubbleManager.updateBubbles(
                        currentTime = currentTime,
                        canvasSize = this.size,
                        waterLevel = animatedScore,
                        deltaTime = deltaTime
                    )
                    lastBubbleUpdateTime = currentTime
                    bubbleManager.getBubbles()
                } else {
                    emptyList()
                }

                drawWaterBall(
                    size = this.size,
                    score = animatedScore,
                    waveOffset = if (isAnimating) waveOffset else 0f,
                    waterColor = waterColor,
                    advancedColorSet = advancedColorSet,
                    isAdvancedMaterialEnabled = isAdvancedMaterialEnabled,
                    internalBubbles = internalBubbles,
                    rippleOffset = if (particleAnimationState == ParticleAnimationState.CONTRACTING ||
                                      particleAnimationState == ParticleAnimationState.EXPANDING_STATIC) rippleOffset else 0f,
                    showRippleEffect = particleAnimationState == ParticleAnimationState.CONTRACTING ||
                                      particleAnimationState == ParticleAnimationState.EXPANDING_STATIC,
                    isWaveReserveSpaceEnabled = isWaveReserveSpaceEnabled,
                    waveStopThreshold = waveStopThreshold,
                    showGrid = showGrid,
                    motionSensingEnabled = motionSensingEnabled,
                    motionSensingManager = motionSensingManager
                )
            }

            // 分数文字显示
            ScoreDisplay(
                score = score,
                textColor = Color.White
            )
        }
    }
}

/**
 * 绘制水球主体
 */
private fun DrawScope.drawWaterBall(
    size: Size,
    score: Float,
    waveOffset: Float,
    waterColor: Color,
    advancedColorSet: WaterBallColorSet? = null,
    isAdvancedMaterialEnabled: Boolean = false,
    internalBubbles: List<InternalBubble> = emptyList(),
    rippleOffset: Float = 0f,
    showRippleEffect: Boolean = false,
    isWaveReserveSpaceEnabled: Boolean = false,
    waveStopThreshold: Int = 80,
    showGrid: Boolean = false,
    motionSensingEnabled: Boolean = false,
    motionSensingManager: MotionSensingManager? = null
) {
    val center = Offset(size.width / 2, size.height / 2)
    val radius = minOf(size.width, size.height) / 2 - 8.dp.toPx()
    
    // 绘制主圆形边框（根据高级材质状态选择合适的边框颜色）
    val borderColor = if (isAdvancedMaterialEnabled && advancedColorSet != null) {
        // 高级材质模式：使用圆环强调色
        WaterBallAdvancedColorSchemes.getRingColor(advancedColorSet, alpha = 0.15f)
    } else {
        // 标准模式：使用水球颜色
        waterColor.copy(alpha = 0.15f)
    }

    drawCircle(
        color = borderColor,
        radius = radius,
        center = center,
        style = Stroke(width = 0.5f.dp.toPx())
    )

    // 绘制涟漪效果（仅在体检/优化期间显示）
    if (showRippleEffect) {
        val rippleCount = 3 // 涟漪圈数
        for (i in 0 until rippleCount) {
            val ripplePhase = (rippleOffset + i * PI.toFloat() / rippleCount) % (2 * PI.toFloat())
            val rippleAlpha = (sin(ripplePhase) + 1f) / 2f * 0.1f // 0-0.1的透明度
            val rippleRadius = radius + sin(ripplePhase) * 8f // 轻微的半径变化

            if (rippleAlpha > 0.01f) { // 只绘制可见的涟漪
                drawCircle(
                    color = waterColor.copy(alpha = rippleAlpha),
                    radius = rippleRadius,
                    center = center,
                    style = Stroke(width = 1.5f.dp.toPx())
                )
            }
        }
    }

    // 创建圆形裁剪路径
    val clipPath = Path().apply {
        addOval(androidx.compose.ui.geometry.Rect(
            center = center,
            radius = radius
        ))
    }
    
    // 在圆形区域内绘制水体
    clipPath(clipPath) {
        // 不绘制背景，保持透明
        
        // 计算水位高度
        val effectiveScore = if (isWaveReserveSpaceEnabled) {
            // 相对映射：将0-100分映射到0-阈值%的水位范围
            // 这样所有分数都有相应的水位变化，保持比例关系
            val mapped = score * (waveStopThreshold / 100f)
            Log.d("WaterBall", "Wave reserve space mapping: $score -> $mapped (threshold: $waveStopThreshold)")
            mapped
        } else {
            score
        }
        // 获取体感跟随偏移量
        val (tiltOffsetX, tiltOffsetY) = if (motionSensingEnabled && motionSensingManager != null) {
            motionSensingManager.getWaterSurfaceOffset(maxOffset = 100f) // 增加最大偏移，实现真实水杯效果
        } else {
            Pair(0f, 0f)
        }

        // 计算水位高度
        val waterLevel = effectiveScore * size.height

        // 基础水位（不考虑重力）
        val baseWaterTop = size.height - waterLevel

        // 根据重力方向调整水位
        val waterTop = if (motionSensingEnabled && motionSensingManager != null) {
            val tiltY = motionSensingManager.tiltY.value
            // 简单的重力调整：当手机倾斜时，水位会相应偏移
            // 限制偏移范围，避免水完全消失或溢出
            val gravityOffset = (tiltY / 90f) * (size.height * 0.3f) // 最大偏移30%的高度
            (baseWaterTop + gravityOffset).coerceIn(0f, size.height)
        } else {
            baseWaterTop
        }


        // 先绘制水面波浪层（在后面，作为背景波浪）
        val surfaceWavePath = createSurfaceWavePath(
            width = size.width,
            height = size.height,
            waterTop = waterTop,
            waveOffset = waveOffset,
            tiltOffsetX = tiltOffsetX,
            tiltOffsetY = tiltOffsetY
        )

        // 根据是否启用高级材质选择不同的绘制方式
        if (isAdvancedMaterialEnabled && advancedColorSet != null) {
            // 高级材质模式：使用渐变和多层效果
            drawAdvancedWaterBall(
                surfaceWavePath = surfaceWavePath,
                size = size,
                waterTop = waterTop,
                waveOffset = waveOffset,
                colorSet = advancedColorSet,
                tiltOffsetX = tiltOffsetX,
                tiltOffsetY = tiltOffsetY
            )
        } else {
            // 标准模式：使用原有的单色绘制
            drawStandardWaterBall(
                surfaceWavePath = surfaceWavePath,
                size = size,
                waterTop = waterTop,
                waveOffset = waveOffset,
                waterColor = waterColor,
                tiltOffsetX = tiltOffsetX,
                tiltOffsetY = tiltOffsetY
            )
        }

        // 绘制网格（仅在天空蓝主题且开启高级材质时，在水面之上）- 仅在允许显示网格时绘制
        if (isAdvancedMaterialEnabled && advancedColorSet != null && showGrid) {
            drawWaterBallGrid(
                size = size,
                center = Offset(size.width / 2, size.height / 2),
                radius = minOf(size.width, size.height) / 2 - 8.dp.toPx()
            )
        }

        // 绘制内部气泡（如果有）
        internalBubbles.forEach { bubble ->
            val bubbleX = bubble.currentX + bubble.swayOffset // 使用当前X位置加上摆动偏移
            val bubbleY = bubble.currentY

            // 确保气泡在水体内（根据当前水位）
            if (bubbleY >= waterTop && bubbleY <= size.height) {
                val bubbleRadius = bubble.radius.dp.toPx()
                val center = Offset(bubbleX, bubbleY)

                // 绘制气泡外圈（很淡的边框）
                drawCircle(
                    color = Color.White.copy(alpha = bubble.alpha * 0.15f),
                    radius = bubbleRadius,
                    center = center,
                    style = Stroke(width = 0.8f.dp.toPx())
                )

                // 绘制气泡内部（使用径向渐变，中心透明，边缘稍微不透明）
                val gradient = Brush.radialGradient(
                    colors = listOf(
                        Color.White.copy(alpha = bubble.alpha * 0.02f), // 中心几乎完全透明
                        Color.White.copy(alpha = bubble.alpha * 0.12f)  // 边缘稍微不透明
                    ),
                    center = center,
                    radius = bubbleRadius * 0.85f
                )
                drawCircle(
                    brush = gradient,
                    radius = bubbleRadius * 0.85f,
                    center = center
                )

                // 绘制气泡高光（左上角的小亮点）
                val highlightRadius = bubbleRadius * 0.25f
                val highlightOffset = bubbleRadius * 0.35f
                drawCircle(
                    color = Color.White.copy(alpha = bubble.alpha * 0.6f),
                    radius = highlightRadius,
                    center = Offset(
                        bubbleX - highlightOffset,
                        bubbleY - highlightOffset
                    )
                )

                // 绘制次要高光（右下角的更小亮点）
                val secondaryHighlightRadius = bubbleRadius * 0.12f
                val secondaryHighlightOffset = bubbleRadius * 0.5f
                drawCircle(
                    color = Color.White.copy(alpha = bubble.alpha * 0.3f),
                    radius = secondaryHighlightRadius,
                    center = Offset(
                        bubbleX + secondaryHighlightOffset * 0.7f,
                        bubbleY + secondaryHighlightOffset * 0.7f
                    )
                )
            }
        }

    }
}



/**
 * 根据水位计算动态波浪幅度
 *
 * 物理真实性：水位低时起伏幅度大，水位高时起伏幅度小
 *
 * @param waterLevel 水位（0-1，0表示无水，1表示满水）
 * @param maxAmplitude 最大波浪幅度（水位为0时的幅度）
 * @param minAmplitude 最小波浪幅度（水位为1时的幅度）
 * @return 计算得出的波浪幅度
 */
private fun calculateWaveAmplitude(
    waterLevel: Float,
    maxAmplitude: Float = 80f,  // 大幅增加最大幅度到80f，让低水位时非常明显
    minAmplitude: Float = 3f    // 进一步减小最小幅度到3f
): Float {
    // 使用更强的反比函数：amplitude = maxAmplitude * (1 - waterLevel^0.5) + minAmplitude
    // 使用0.5次方（平方根）让低水位时的变化极其明显
    val normalizedLevel = waterLevel.coerceIn(0f, 1f)
    val amplitudeRange = maxAmplitude - minAmplitude
    val dampingFactor = 1f - normalizedLevel.pow(0.5f)  // 从0.8f改为0.5f，让变化更加剧烈

    return minAmplitude + amplitudeRange * dampingFactor
}

/**
 * 创建主水体路径（带有大幅度波浪）
 */
private fun createMainWaterPath(
    width: Float,
    height: Float,
    waterTop: Float,
    waveOffset: Float,
    tiltOffsetX: Float = 0f,
    tiltOffsetY: Float = 0f
): Path {
    return Path().apply {
        // 如果水位为0，返回空路径
        if (waterTop >= height) {
            return@apply
        }

        // 从底部开始
        moveTo(0f, height)
        lineTo(0f, waterTop)

        // 计算当前水位（0-1）
        val waterLevel = 1f - (waterTop / height)

        // 创建主波浪曲线（动态幅度波浪）
        val waveLength = width * 1.2f // 增大波长，让整个水球只有一点波浪起伏
        val waveHeight = calculateWaveAmplitude(waterLevel) // 使用动态波浪幅度
        val steps = (width / 2f).toInt()

        for (i in 0..steps) {
            val x = (i * width / steps)
            // 修复波浪连贯性：使用 + waveOffset 而不是 - waveOffset，确保动画循环时连贯
            // 添加体感跟随偏移（真实水杯效果）
            val baseWaveY = waterTop + sin((x / waveLength + waveOffset) * 2 * PI) * waveHeight
            // 修正方向：反转X轴偏移，Y轴偏移也反转以实现正确的重力效果
            val tiltedWaveY = baseWaveY - tiltOffsetY * 2f + (-tiltOffsetX * (x - width / 2) / width * 2f)
            lineTo(x, tiltedWaveY.toFloat())
        }

        // 封闭路径
        lineTo(width, height)
        close()
    }
}

/**
 * 创建水面波浪层路径（较浅的波浪，在水面附近）
 */
private fun createSurfaceWavePath(
    width: Float,
    height: Float,
    waterTop: Float,
    waveOffset: Float,
    tiltOffsetX: Float = 0f,
    tiltOffsetY: Float = 0f
): Path {
    return Path().apply {
        // 如果水位为0，返回空路径
        if (waterTop >= height) {
            return@apply
        }

        // 从底部开始
        moveTo(0f, height)
        lineTo(0f, waterTop)

        // 计算当前水位（0-1）
        val waterLevel = 1f - (waterTop / height)

        // 创建水面波浪曲线（与主水体不同的频率和相位，形成交叉效果）
        val waveLength = width * 0.9f // 与主波浪有明显不同的波长，避免重叠
        val waveHeight = calculateWaveAmplitude(waterLevel, maxAmplitude = 70f, minAmplitude = 2f) // 使用稍微不同但同样明显的幅度参数
        val steps = (width / 2f).toInt()

        for (i in 0..steps) {
            val x = (i * width / steps)
            // 修复波浪连贯性：使用 + waveOffset 确保动画循环时连贯，同时保持不同的相位和频率
            // 添加体感跟随偏移（与主水体保持一致的倾斜率）
            val baseWaveY = waterTop + sin((x / waveLength + waveOffset * 0.6 + PI) * 2 * PI) * waveHeight
            // 与主水体相同的倾斜效果，修正方向
            val tiltedWaveY = baseWaveY - tiltOffsetY * 2f + (-tiltOffsetX * (x - width / 2) / width * 2f)
            lineTo(x, tiltedWaveY.toFloat())
        }

        // 封闭路径
        lineTo(width, height)
        close()
    }
}



/**
 * 分数显示组件
 */
@Composable
private fun ScoreDisplay(
    score: Int,
    textColor: Color
) {
    Row(
        verticalAlignment = Alignment.Bottom,
        horizontalArrangement = Arrangement.Center
    ) {
        // 主分数数字
        Text(
            text = score.toString(),
            style = TextStyle(
                fontSize = 48.sp,
                fontWeight = FontWeight.Bold,
                color = textColor,
                shadow = Shadow(
                    color = Color.Black.copy(alpha = 0.3f),
                    offset = Offset(2f, 2f),
                    blurRadius = 4f
                )
            )
        )
        
        // "分"字
        Text(
            text = "分",
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = textColor,
                shadow = Shadow(
                    color = Color.Black.copy(alpha = 0.3f),
                    offset = Offset(1f, 1f),
                    blurRadius = 2f
                )
            ),
            modifier = Modifier.padding(bottom = 8.dp, start = 4.dp)
        )
    }
}

/**
 * 增强的装饰性粒子效果 - 支持分层波纹收缩和螺旋爆发动画
 */
@Composable
private fun EnhancedDecorativeParticles(
    modifier: Modifier = Modifier,
    particleManager: DecorativeParticleManager,
    bubbleColor: Color,
    waveOffset: Float,
    waterBallSize: Dp
) {
    Canvas(modifier = modifier) {
        val centerX = size.width / 2f
        val centerY = size.height / 2f

        // 更新粒子状态
        particleManager.updateParticles(
            currentTime = System.currentTimeMillis(),
            waveOffset = waveOffset,
            waterBallCenterX = centerX,
            waterBallCenterY = centerY
        )

        // 获取当前粒子并绘制
        val particles = particleManager.getParticles()
        val currentState = particleManager.getState()
        println("EnhancedDecorativeParticles: Drawing ${particles.size} particles, state=$currentState")

        // 如果没有粒子，打印警告并返回
        if (particles.isEmpty()) {
            println("EnhancedDecorativeParticles: Warning - Expected 10 particles, got 0")
            return@Canvas
        }

        particles.forEach { particle ->
            // 将极坐标转换为笛卡尔坐标
            val radians = Math.toRadians(particle.currentAngle.toDouble())
            val x = centerX + particle.currentDistance * cos(radians).toFloat()
            val y = centerY + particle.currentDistance * sin(radians).toFloat()

            // 计算粒子颜色（考虑透明度）
            val particleColor = bubbleColor.copy(alpha = bubbleColor.alpha * particle.currentAlpha)



            // 绘制粒子主体
            drawCircle(
                color = particleColor,
                radius = particle.currentRadius,
                center = Offset(x, y)
            )

            // 为移动中的粒子添加轻微的尾迹效果
            if (particleManager.getState() == ParticleAnimationState.CONTRACTING ||
                particleManager.getState() == ParticleAnimationState.EXPANDING_STATIC) {

                // 计算尾迹位置（稍微滞后的位置）
                val trailDistance = particle.currentDistance + 8f
                val trailX = centerX + trailDistance * cos(radians).toFloat()
                val trailY = centerY + trailDistance * sin(radians).toFloat()

                // 绘制尾迹（更小、更透明）
                drawCircle(
                    color = particleColor.copy(alpha = particleColor.alpha * 0.3f),
                    radius = particle.currentRadius * 0.6f,
                    center = Offset(trailX, trailY)
                )
            }
        }
    }
}

/**
 * 装饰性气泡效果 - 绘制在水球周围（原版本，保留作为备用）
 */
@Composable
private fun DecorativeBubbles(
    modifier: Modifier = Modifier,
    bubbleColor: Color,
    waveOffset: Float,
    waterBallSize: Dp
) {
    Canvas(modifier = modifier) {
        val centerX = size.width / 2f
        val centerY = size.height / 2f
        val waterBallRadius = waterBallSize.toPx() / 2f

        // 定义气泡在水球周围的位置（使用极坐标）
        val bubbles = listOf(
            // 右上角区域
            Triple(45f, waterBallRadius + 20.dp.toPx(), 3.dp.toPx()),
            Triple(75f, waterBallRadius + 35.dp.toPx(), 2.dp.toPx()),
            Triple(15f, waterBallRadius + 45.dp.toPx(), 2.5f.dp.toPx()),

            // 左上角区域
            Triple(135f, waterBallRadius + 25.dp.toPx(), 2.dp.toPx()),
            Triple(165f, waterBallRadius + 40.dp.toPx(), 1.8f.dp.toPx()),
            Triple(105f, waterBallRadius + 30.dp.toPx(), 2.2f.dp.toPx()),

            // 右下角区域
            Triple(315f, waterBallRadius + 30.dp.toPx(), 1.5f.dp.toPx()),
            Triple(345f, waterBallRadius + 20.dp.toPx(), 2.8f.dp.toPx()),

            // 左下角区域
            Triple(225f, waterBallRadius + 35.dp.toPx(), 2.3f.dp.toPx()),
            Triple(195f, waterBallRadius + 25.dp.toPx(), 1.7f.dp.toPx())
        )

        // 绘制气泡，添加轻微的浮动效果
        bubbles.forEachIndexed { index, (angle, distance, radius) ->
            val floatOffset = sin(waveOffset + index * 0.8f) * 5f
            val actualDistance = distance + floatOffset

            // 将极坐标转换为笛卡尔坐标
            val radians = Math.toRadians(angle.toDouble())
            val x = centerX + actualDistance * cos(radians).toFloat()
            val y = centerY + actualDistance * sin(radians).toFloat()

            drawCircle(
                color = bubbleColor,
                radius = radius,
                center = Offset(x, y)
            )
        }
    }
}

/**
 * 静态进度弧线组件 - 在非处理状态下显示逐渐增长的弧线
 */
@Composable
private fun StaticProgressArcAnimation(
    modifier: Modifier = Modifier,
    arcColor: Color,
    progress: Float, // 0.0 到 1.0 的进度值
    arcAnimationProgress: Float, // 弧线动画进度 0.0 到 1.0
    waterBallSize: Dp
) {
    Canvas(modifier = modifier) {
        val centerX = size.width / 2f
        val centerY = size.height / 2f

        // 精确计算水球的实际半径
        val waterBallActualRadius = waterBallSize.toPx() / 2f - 8.dp.toPx()

        // 弧线半径比水球边框稍大一些，保持适当间距
        val arcRadius = waterBallActualRadius + 4.dp.toPx()

        // 绘制围绕水球的静态弧线（固定角度，带动画）
        // Canvas角度系统：-90度是顶部，0度是右侧，90度是底部，180度是左侧
        val startAngle = -90f // 从顶部开始
        val fixedArcLength = 315f // 固定弧线长度315度
        val animatedArcLength = fixedArcLength * arcAnimationProgress // 动画控制弧线长度

        // 只有当动画值大于0时才绘制弧线（不再依赖分数）
        if (arcAnimationProgress > 0f) {
            // 绘制静态弧线（固定90度，从0度逐渐增长）
            drawArc(
                color = arcColor,
                startAngle = startAngle,
                sweepAngle = animatedArcLength,
                useCenter = false,
                topLeft = Offset(
                    centerX - arcRadius,
                    centerY - arcRadius
                ),
                size = Size(arcRadius * 2, arcRadius * 2),
                style = Stroke(
                    width = 1.5f.dp.toPx(),
                    cap = StrokeCap.Round
                )
            )

            // 绘制弧线末端的小圆点
            val endAngleInRadians = Math.toRadians((startAngle + animatedArcLength).toDouble())
            val endX = centerX + arcRadius * cos(endAngleInRadians).toFloat()
            val endY = centerY + arcRadius * sin(endAngleInRadians).toFloat()

            drawCircle(
                color = arcColor.copy(alpha = 1f),
                radius = 2f.dp.toPx(),
                center = Offset(endX, endY)
            )
        }
    }
}

/**
 * 外围弧线动画组件 - 在体检和优化时显示旋转弧线
 */
@Composable
private fun ProcessingArcAnimation(
    modifier: Modifier = Modifier,
    arcColor: Color,
    rotation: Float,
    waterBallSize: Dp,
    waterBallScale: Float = 1.0f // 新增：水球当前的缩放比例
) {
    Canvas(modifier = modifier) {
        val centerX = size.width / 2f
        val centerY = size.height / 2f

        // 精确计算水球的实际半径
        // 水球在内层容器中的实际半径 = (waterBallSize / 2) - 8.dp
        val waterBallActualRadius = waterBallSize.toPx() / 2f - 8.dp.toPx()

        // 考虑水球的当前缩放比例（包括呼吸效果）
        val scaledWaterBallRadius = waterBallActualRadius * waterBallScale

        // 弧线半径应该比缩放后的水球边框稍大一些，保持与静态弧线相同的间距
        // 使用与静态进度弧线相同的间距（4.dp），确保视觉一致性
        val arcRadius = scaledWaterBallRadius + 4.dp.toPx()

        // 绘制旋转弧线
        drawArc(
            color = arcColor,
            startAngle = rotation,
            sweepAngle = 90f, // 弧线长度90度（稍微减短，更加优雅）
            useCenter = false,
            topLeft = Offset(
                centerX - arcRadius,
                centerY - arcRadius
            ),
            size = Size(arcRadius * 2, arcRadius * 2),
            style = Stroke(
                width = 1.5f.dp.toPx(), // 调细弧线，更加优雅
                cap = StrokeCap.Round
            )
        )

        // 绘制弧线末端的小圆点（增强视觉效果）
        val endAngle = Math.toRadians((rotation + 90f).toDouble()) // 更新为90度
        val endX = centerX + arcRadius * cos(endAngle).toFloat()
        val endY = centerY + arcRadius * sin(endAngle).toFloat()

        // 绘制更明显的末端圆点，没有透明度
        drawCircle(
            color = arcColor.copy(alpha = 1f), // 完全不透明
            radius = 2f.dp.toPx(), // 调整圆点大小
            center = Offset(endX, endY)
        )
    }
}

/**
 * 绘制标准水球（原有的单色模式）
 */
private fun DrawScope.drawStandardWaterBall(
    surfaceWavePath: Path,
    size: Size,
    waterTop: Float,
    waveOffset: Float,
    waterColor: Color,
    tiltOffsetX: Float = 0f,
    tiltOffsetY: Float = 0f
) {
    // 绘制主水体
    drawPath(
        path = surfaceWavePath,
        color = waterColor.copy(alpha = 0.4f) // 较低透明度，作为背景
    )

    // 再绘制主水体路径（在前面，覆盖部分波浪层）
    val mainWaterPath = createMainWaterPath(
        width = size.width,
        height = size.height,
        waterTop = waterTop,
        waveOffset = waveOffset,
        tiltOffsetX = tiltOffsetX, // 主水体也应用倾斜效果
        tiltOffsetY = tiltOffsetY
    )

    drawPath(
        path = mainWaterPath,
        color = waterColor.copy(alpha = 1.0f) // 完全不透明，避免透过后面的波浪层
    )
}

/**
 * 绘制高级材质水球（渐变和多层效果）
 */
private fun DrawScope.drawAdvancedWaterBall(
    surfaceWavePath: Path,
    size: Size,
    waterTop: Float,
    waveOffset: Float,
    colorSet: WaterBallColorSet,
    tiltOffsetX: Float = 0f,
    tiltOffsetY: Float = 0f
) {
    // 创建主水体路径
    val mainWaterPath = createMainWaterPath(
        width = size.width,
        height = size.height,
        waterTop = waterTop,
        waveOffset = waveOffset,
        tiltOffsetX = tiltOffsetX, // 主水体也应用倾斜效果
        tiltOffsetY = tiltOffsetY
    )

    // 创建垂直渐变（从底部到顶部）
    val gradientBrush = Brush.verticalGradient(
        colors = WaterBallAdvancedColorSchemes.getGradientColors(colorSet),
        startY = size.height,
        endY = waterTop
    )

    // 绘制主水体（使用渐变）
    drawPath(
        path = mainWaterPath,
        brush = gradientBrush
    )

    // 绘制表面波浪层（使用渐变结束色，与水体表面颜色协调）
    drawPath(
        path = surfaceWavePath,
        color = colorSet.gradientEnd.copy(alpha = 0.4f)
    )
}

/**
 * 绘制水球内部网格
 * 实现从中心向外围逐渐透明化的视觉效果
 */
private fun DrawScope.drawWaterBallGrid(
    size: Size,
    center: Offset,
    radius: Float
) {
    val gridSpacing = 20f // 网格间距（像素），更密集

    // 创建径向渐变Paint，实现从中心向外围透明化
    val gridBrush = Brush.radialGradient(
        colors = listOf(
            Color(0xBBFFFFFF), // 中心颜色：半透明浅蓝色/白色
            Color(0x00FFFFFF)  // 边缘颜色：完全透明
        ),
        center = center,
        radius = radius,
        tileMode = TileMode.Clamp
    )

    // 绘制垂直网格线
    var x = center.x % gridSpacing
    while (x <= size.width) {
        drawLine(
            brush = gridBrush,
            start = Offset(x, 0f),
            end = Offset(x, size.height),
            strokeWidth = 1.dp.toPx(),
            alpha = 0.1f // 增加整体透明度控制
        )
        x += gridSpacing
    }

    // 绘制水平网格线
    var y = center.y % gridSpacing
    while (y <= size.height) {
        drawLine(
            brush = gridBrush,
            start = Offset(0f, y),
            end = Offset(size.width, y),
            strokeWidth = 1.dp.toPx(),
            alpha = 0.1f // 增加整体透明度控制
        )
        y += gridSpacing
    }
}

/**
 * 静态水球组件
 *
 * 专为首次进入状态设计的静态水球，包含心电图和扫描弧线动画
 * 特点：
 * - 蓝色圆球背景
 * - 心电图波形绘制动画
 * - 双向扫描弧线动画
 * - 根据主题显示不同颜色
 * - 支持天空蓝主题高级材质效果
 */
@Composable
fun StaticWaterBallComponent(
    modifier: Modifier = Modifier,
    size: Dp = 200.dp,
    showGrid: Boolean = false, // 控制是否显示网格，默认不显示
    shouldStartContraction: Boolean = false, // 是否开始收缩动画
    onContractionComplete: () -> Unit = {} // 收缩动画完成回调
) {
    // 获取主题上下文和设置
    val themeContext = LocalThemeContext.current
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 判断是否启用高级材质效果（仅在天空蓝主题下有效）
    val isAdvancedMaterialEnabled = remember(globalSettings.waterBallAdvancedMaterialEnabled, themeContext.theme.id) {
        globalSettings.waterBallAdvancedMaterialEnabled && themeContext.theme.id == "sky_blue"
    }

    // 根据主题确定水球颜色（使用品牌色）
    val waterColor = themeContext.colorScheme.primary

    // 获取高级材质颜色方案（满水状态使用优秀等级的颜色）
    val advancedColorSet = remember(isAdvancedMaterialEnabled) {
        if (isAdvancedMaterialEnabled) {
            WaterBallAdvancedColorSchemes.getColorSetForScore(100) // 满水状态使用100分的颜色
        } else {
            null
        }
    }

    // 动画状态
    var animationStarted by remember { mutableStateOf(false) }
    val ecgProgress by animateFloatAsState(
        targetValue = if (animationStarted) 1f else 0f,
        animationSpec = tween(durationMillis = 1000, easing = LinearEasing), // 调回1秒
        label = "ecg_progress"
    )

    val arcRotationProgress by animateFloatAsState(
        targetValue = if (animationStarted && ecgProgress > 0.1f) 1f else 0f, // 改为10%时开始
        animationSpec = tween(
            durationMillis = 1500, // 比心电图动画慢500ms
            easing = CubicBezierEasing(0.4f, 0.0f, 0.2f, 1.0f) // 先快后慢的贝塞尔曲线，更明显的减速效果
        ),
        label = "arc_rotation_progress"
    )

    // 启动动画 - 立即开始，不延迟
    LaunchedEffect(Unit) {
        animationStarted = true
    }

    // 装饰性粒子管理器
    val particleManager = remember { DecorativeParticleManager() }
    val density = LocalDensity.current

    // 收缩动画状态
    var isContracting by remember { mutableStateOf(false) }
    var isProcessing by remember { mutableStateOf(false) }

    // 无限循环动画
    val infiniteTransition = rememberInfiniteTransition(label = "static_water_ball_infinite")

    // 波浪偏移动画 - 用于粒子的正常状态漂浮效果
    val waveOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(9000, easing = LinearEasing), // 与WaterBallComponent保持一致
            repeatMode = RepeatMode.Restart
        ),
        label = "static_wave_offset"
    )

    // 水球缩放动画
    val waterBallScale by animateFloatAsState(
        targetValue = if (isContracting) 0.9f else 1.0f,
        animationSpec = tween(
            durationMillis = DecorativeParticleManager.WATER_BALL_CONTRACTION_DURATION.toInt(),
            easing = EaseInOutCubic
        ),
        label = "static_water_ball_scale"
    )

    // 呼吸动画 - 仅在收缩后的处理期间启用
    val breathingScale by infiniteTransition.animateFloat(
        initialValue = 1.0f,
        targetValue = 1.02f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "static_breathing_scale"
    )

    // 涟漪效果动画 - 仅在收缩后的处理期间启用
    val rippleOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "static_ripple_offset"
    )

    // 计算最终的水球缩放比例（结合大小动画和呼吸动画）
    val finalWaterBallScale = if (isProcessing) {
        waterBallScale * breathingScale  // 处理期间应用呼吸效果（颤抖）
    } else {
        waterBallScale  // 非处理状态不应用呼吸效果，不颤抖
    }

    // 监听收缩动画触发
    LaunchedEffect(shouldStartContraction) {
        if (shouldStartContraction && !isContracting) {
            println("StaticWaterBallComponent: Starting contraction animation")
            isContracting = true

            // 启动粒子收缩动画
            if (particleManager.getParticles().isNotEmpty()) {
                particleManager.startContraction()
            }

            // 等待收缩动画完成
            val contractionDuration = 1200L + (3 - 1) * 200L // CONTRACTION_DURATION + (TOTAL_LAYERS - 1) * LAYER_DELAY
            delay(contractionDuration)

            println("StaticWaterBallComponent: Contraction completed, entering processing state")
            // 收缩完成后进入处理状态，启用呼吸动画
            isProcessing = true

            println("StaticWaterBallComponent: Contraction animation completed")
            onContractionComplete()
        }
    }

    // 初始化粒子 - 确保粒子在首次渲染时就被初始化
    LaunchedEffect(size) {
        val waterBallRadius = with(density) { size.toPx() } / 2f
        println("StaticWaterBallComponent: Initializing particles with radius $waterBallRadius")
        particleManager.initializeParticles(waterBallRadius) { dp -> with(density) { dp.dp.toPx() } }
        // 初始化完成后，确保粒子处于正常状态
        particleManager.resetToNormal()
        println("StaticWaterBallComponent: Particles initialized (${particleManager.getParticles().size}) and reset to normal, state=${particleManager.getState()}")
    }

    // 立即初始化粒子，避免绘制时粒子为空
    val waterBallRadius = with(density) { size.toPx() } / 2f
    if (particleManager.getParticles().isEmpty()) {
        println("StaticWaterBallComponent: Immediate particle initialization with radius $waterBallRadius")
        particleManager.initializeParticles(waterBallRadius) { dp -> with(density) { dp.dp.toPx() } }
        particleManager.resetToNormal()
        println("StaticWaterBallComponent: Immediate particles initialized (${particleManager.getParticles().size})")
    }

    // 使用更大的容器来容纳水球和周围的粒子（与WaterBallComponent保持一致）
    Box(
        modifier = modifier.size(size * 1.4f), // 扩大容器以容纳周围粒子
        contentAlignment = Alignment.Center
    ) {
        // 装饰性粒子效果 - 放在水球后面
        EnhancedDecorativeParticles(
            modifier = Modifier.fillMaxSize(),
            particleManager = particleManager,
            bubbleColor = waterColor.copy(alpha = 0.3f),
            waveOffset = waveOffset, // 使用动画的波浪偏移，让粒子能够正常漂浮
            waterBallSize = size
        )

        // 水球主体容器 - 应用大小动画和呼吸效果（与WaterBallComponent保持一致）
        Box(
            modifier = Modifier.size(size * finalWaterBallScale),
            contentAlignment = Alignment.Center
        ) {
            // 静态水球绘制
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .clipToBounds()
            ) {
                drawStaticWaterBallWithAnimation(
                    size = this.size,
                    waterColor = waterColor,
                    advancedColorSet = advancedColorSet,
                    isAdvancedMaterialEnabled = isAdvancedMaterialEnabled,
                    ecgProgress = ecgProgress,
                    arcRotationProgress = arcRotationProgress,
                    showGrid = showGrid,
                    isProcessing = isProcessing,
                    rippleOffset = rippleOffset
                )
            }
        }

        // 静态进度弧线 - 显示在水球外围（仅在非收缩状态下显示）
        if (!isContracting && !isProcessing) {
            StaticProgressArcAnimation(
                modifier = Modifier.fillMaxSize(),
                arcColor = if (advancedColorSet != null) {
                    advancedColorSet.gradientEnd.copy(alpha = 0.8f)
                } else {
                    waterColor.copy(alpha = 0.8f)
                },
                progress = 1f, // 静态水球显示满进度
                arcAnimationProgress = 1f, // 静态显示完整弧线
                waterBallSize = size
            )
        }
    }
}



/**
 * 绘制带动画的静态水球
 */
private fun DrawScope.drawStaticWaterBallWithAnimation(
    size: Size,
    waterColor: Color,
    advancedColorSet: WaterBallColorSet? = null,
    isAdvancedMaterialEnabled: Boolean = false,
    ecgProgress: Float,
    arcRotationProgress: Float,
    showGrid: Boolean = false,
    isProcessing: Boolean = false,
    rippleOffset: Float = 0f
) {
    val center = Offset(size.width / 2, size.height / 2)
    val radius = minOf(size.width, size.height) / 2 - 8.dp.toPx()

    // 绘制主圆形边框（根据高级材质状态选择合适的边框颜色）
    val borderColor = if (isAdvancedMaterialEnabled && advancedColorSet != null) {
        // 高级材质模式：使用圆环强调色
        WaterBallAdvancedColorSchemes.getRingColor(advancedColorSet, alpha = 0.15f)
    } else {
        // 标准模式：使用水球颜色
        waterColor.copy(alpha = 0.15f)
    }

    drawCircle(
        color = borderColor,
        radius = radius,
        center = center,
        style = Stroke(width = 0.5f.dp.toPx())
    )

    // 创建圆形裁剪路径
    val clipPath = Path().apply {
        addOval(androidx.compose.ui.geometry.Rect(
            center = center,
            radius = radius
        ))
    }

    // 在圆形区域内绘制满水状态
    clipPath(clipPath) {
        // 根据是否启用高级材质选择不同的绘制方式
        if (isAdvancedMaterialEnabled && advancedColorSet != null) {
            // 高级材质模式：使用渐变效果（满水状态）
            val gradientBrush = Brush.verticalGradient(
                colors = WaterBallAdvancedColorSchemes.getGradientColors(advancedColorSet),
                startY = size.height,
                endY = 0f // 满水状态，从底部到顶部
            )

            drawRect(
                brush = gradientBrush,
                topLeft = Offset(0f, 0f),
                size = size
            )

            // 绘制网格（在水面之上）- 仅在允许显示网格时绘制
            if (showGrid) {
                drawWaterBallGrid(
                    size = size,
                    center = Offset(size.width / 2, size.height / 2),
                    radius = minOf(size.width, size.height) / 2 - 8.dp.toPx()
                )
            }
        } else {
            // 标准模式：使用原有的单色绘制
            // 绘制满水状态（水位100%）
            drawRect(
                color = waterColor,
                topLeft = Offset(0f, 0f),
                size = size
            )

            // 添加轻微的渐变效果以增加质感
            val gradientBrush = Brush.verticalGradient(
                colors = listOf(
                    waterColor.copy(alpha = 0.8f),
                    waterColor,
                    waterColor.copy(alpha = 0.9f)
                ),
                startY = 0f,
                endY = size.height
            )

            drawRect(
                brush = gradientBrush,
                topLeft = Offset(0f, 0f),
                size = size
            )
        }
    }

    // 绘制心电图波形
    if (ecgProgress > 0f) {
        drawECGWaveform(
            size = size,
            center = center,
            radius = radius,
            progress = ecgProgress
        )
    }

    // 绘制扫描弧线
    if (arcRotationProgress > 0f) {
        drawRotatingArcs(
            center = center,
            radius = radius,
            rotationProgress = arcRotationProgress
        )
    }

    // 绘制涟漪效果 - 仅在处理期间显示
    if (isProcessing) {
        drawRippleEffect(
            center = center,
            radius = radius,
            rippleOffset = rippleOffset,
            waterColor = waterColor
        )
    }
}

/**
 * 绘制心电图波形 - 严格按照指定坐标序列
 */
private fun DrawScope.drawECGWaveform(
    size: Size,
    center: Offset,
    radius: Float,
    progress: Float
) {
    val path = Path()
    val waveWidth = radius * 1.6f // 调整波形宽度，确保完全在水球内
    val waveHeight = radius * 0.4f // 减少波形高度，降低振幅
    val centerX = center.x
    val baseY = center.y

    // 严格按照指定坐标序列的ECG波形关键点
    // 坐标已经按照相对位置转换为归一化坐标（0-1之间）
    val ecgPoints = listOf(
        // 【开始平稳段】：15%时间
        0.0f to 0.0f,    // 起点
        0.15f to 0.0f,   // 平稳段结束

        // 【小幅下降】：5%时间
        0.2f to -0.25f,  // 小低谷

        // 【拉升至最高峰】：15%时间（加宽第一个山峰）
        0.35f to 1.0f,   // 最高峰

        // 【下降至半山腰】：5%时间
        0.4f to 0.0f,    // 半山腰

        // 【小幅回升】：5%时间
        0.45f to 0.3f,   // 次级小高峰

        // 【下降至最低谷】：5%时间
        0.5f to -0.6f,   // 最低谷

        // 【大幅回升】：10%时间（缩窄最后一个山峰）
        0.6f to 0.5f,    // 大幅回升

        // 【回到基线】：10%时间
        0.7f to 0.0f,    // 回到基线

        // 【结束平稳段】：15%时间
        0.85f to 0.0f,   // 平稳段开始
        1.0f to 0.0f     // 结束
    )

    // 计算当前应该绘制到哪个点
    val totalLength = ecgPoints.size - 1
    val currentLength = progress * totalLength
    val currentIndex = currentLength.toInt().coerceAtMost(totalLength - 1)
    val segmentProgress = currentLength - currentIndex

    // 构建路径
    for (i in 0..currentIndex) {
        val point = ecgPoints[i]
        val x = centerX + (point.first - 0.5f) * waveWidth + 30f // 居中显示，向右偏移30像素
        val y = baseY - point.second * waveHeight // 注意Y轴方向

        if (i == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }

    // 如果还在绘制过程中，添加部分线段
    if (currentIndex < totalLength - 1 && segmentProgress > 0) {
        val currentPoint = ecgPoints[currentIndex]
        val nextPoint = ecgPoints[currentIndex + 1]

        val currentX = centerX + (currentPoint.first - 0.5f) * waveWidth + 30f
        val currentY = baseY - currentPoint.second * waveHeight
        val nextX = centerX + (nextPoint.first - 0.5f) * waveWidth + 30f
        val nextY = baseY - nextPoint.second * waveHeight

        val interpolatedX = currentX + (nextX - currentX) * segmentProgress
        val interpolatedY = currentY + (nextY - currentY) * segmentProgress

        path.lineTo(interpolatedX, interpolatedY)
    }

    // 绘制心电图线条 - 恢复原来的白色
    drawPath(
        path = path,
        color = Color.White.copy(alpha = 0.3f), // 恢复原来的颜色
        style = Stroke(width = 2.dp.toPx(), cap = StrokeCap.Round, join = StrokeJoin.Round)
    )
}

/**
 * 绘制旋转弧线 - 在水球内部，两条弧线头部在前旋转
 */
private fun DrawScope.drawRotatingArcs(
    center: Offset,
    radius: Float,
    rotationProgress: Float
) {
    val arcLength = 150f // 弧线长度（度数）- 调长弧线
    val strokeWidth = 1.5f.dp.toPx()
    val arcRadius = radius * 0.9f // 在水球内部，距离心电图更远的圆

    // 第一条弧线：从12点钟方向开始，先增长到固定长度，再旋转到9点钟方向
    val arc1StartAngle = 450f // 起始位置：12点钟方向 + 90度 + 60度 = 450度
    val arc1EndAngle = 570f // 结束位置：9点钟方向 + 60度 - 120度

    if (rotationProgress > 0f) {
        // 分两个阶段：前30%增长弧线长度，后70%旋转
        val growthPhase = 0.3f
        if (rotationProgress <= growthPhase) {
            // 第一阶段：弧线从起始角度顺时针增长到固定长度
            val growthProgress = rotationProgress / growthPhase
            val currentSweepAngle = arcLength * growthProgress
            drawArcWithGradient(
                center = center,
                radius = arcRadius,
                startAngle = arc1StartAngle,
                sweepAngle = currentSweepAngle,
                strokeWidth = strokeWidth,
                baseColor = Color.White
            )
        } else {
            // 第二阶段：弧线以固定长度旋转
            val rotationPhaseProgress = (rotationProgress - growthPhase) / (1f - growthPhase)
            val currentStartAngle = arc1StartAngle + (arc1EndAngle - arc1StartAngle) * rotationPhaseProgress
            drawArcWithGradient(
                center = center,
                radius = arcRadius,
                startAngle = currentStartAngle,
                sweepAngle = arcLength,
                strokeWidth = strokeWidth,
                baseColor = Color.White
            )
        }
    }

    // 第二条弧线：从6点钟方向开始，先增长到固定长度，再旋转到3点钟方向
    val arc2StartAngle = 270f // 起始位置：6点钟方向 + 90度 + 60度 = 270度
    val arc2EndAngle = 390f // 结束位置：3点钟方向 + 60度 - 120度

    if (rotationProgress > 0f) {
        // 分两个阶段：前30%增长弧线长度，后70%旋转
        val growthPhase = 0.3f
        if (rotationProgress <= growthPhase) {
            // 第一阶段：弧线从起始角度顺时针增长到固定长度
            val growthProgress = rotationProgress / growthPhase
            val currentSweepAngle = arcLength * growthProgress
            drawArcWithGradient(
                center = center,
                radius = arcRadius,
                startAngle = arc2StartAngle,
                sweepAngle = currentSweepAngle,
                strokeWidth = strokeWidth,
                baseColor = Color.White
            )
        } else {
            // 第二阶段：弧线以固定长度旋转
            val rotationPhaseProgress = (rotationProgress - growthPhase) / (1f - growthPhase)
            val currentStartAngle = arc2StartAngle + (arc2EndAngle - arc2StartAngle) * rotationPhaseProgress
            drawArcWithGradient(
                center = center,
                radius = arcRadius,
                startAngle = currentStartAngle,
                sweepAngle = arcLength,
                strokeWidth = strokeWidth,
                baseColor = Color.White
            )
        }
    }
}

/**
 * 绘制带渐变效果的弧线（头部亮，尾部淡出）
 */
private fun DrawScope.drawArcWithGradient(
    center: Offset,
    radius: Float,
    startAngle: Float,
    sweepAngle: Float,
    strokeWidth: Float,
    baseColor: Color
) {
    // 计算弧线的起始和结束角度（转换为弧度）
    val startAngleRad = Math.toRadians(startAngle.toDouble())
    val endAngleRad = Math.toRadians((startAngle + sweepAngle).toDouble())

    // 计算弧线起始和结束点的坐标
    val startX = center.x + radius * cos(startAngleRad).toFloat()
    val startY = center.y + radius * sin(startAngleRad).toFloat()
    val endX = center.x + radius * cos(endAngleRad).toFloat()
    val endY = center.y + radius * sin(endAngleRad).toFloat()

    // 创建从头部到尾部的线性渐变（头部透明度最高，尾部透明度最低）
    val linearGradient = Brush.linearGradient(
        colors = listOf(
            baseColor.copy(alpha = 0.7f), // 头部：70%透明度（最高）
            baseColor.copy(alpha = 0f)    // 尾部：0%透明度（最低）
        ),
        start = Offset(endX, endY),   // 从头部开始
        end = Offset(startX, startY)  // 到尾部结束
    )

    drawArc(
        brush = linearGradient,
        startAngle = startAngle,
        sweepAngle = sweepAngle,
        useCenter = false,
        topLeft = Offset(center.x - radius, center.y - radius),
        size = Size(radius * 2, radius * 2),
        style = Stroke(width = strokeWidth, cap = StrokeCap.Round)
    )
}

/**
 * 绘制涟漪效果 - 在处理期间显示的同心圆涟漪
 */
private fun DrawScope.drawRippleEffect(
    center: Offset,
    radius: Float,
    rippleOffset: Float,
    waterColor: Color
) {
    val rippleCount = 3
    val maxRippleRadius = radius * 0.8f

    for (i in 0 until rippleCount) {
        val ripplePhase = (rippleOffset + i * 2 * PI.toFloat() / rippleCount) % (2 * PI.toFloat())
        val rippleProgress = (sin(ripplePhase.toDouble()).toFloat() + 1) / 2 // 0 to 1
        val currentRadius = maxRippleRadius * rippleProgress
        val alpha = (1 - rippleProgress) * 0.3f

        if (alpha > 0.01f) {
            drawCircle(
                color = waterColor.copy(alpha = alpha),
                radius = currentRadius,
                center = center,
                style = Stroke(width = 1.dp.toPx())
            )
        }
    }
}




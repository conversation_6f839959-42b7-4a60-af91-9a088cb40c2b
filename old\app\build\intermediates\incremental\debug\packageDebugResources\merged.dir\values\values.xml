<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="accessibility_service_description">在系统无障碍设置中启用"快捷指令-系统优先级提升"服务，提高应用后台存活率</string>
    <string name="accessibility_service_disable">禁用</string>
    <string name="accessibility_service_enable">启用</string>
    <string name="accessibility_service_go_to_settings">前往设置</string>
    <string name="accessibility_service_manual_title">手动提升系统优先级</string>
    <string name="accessibility_service_name">快捷指令-系统优先级提升</string>
    <string name="accessibility_service_system_description">提高应用系统优先级，优化后台存活率</string>
    <string name="accessibility_service_title">系统优先级提升</string>
    <string name="account_selection">选择账号</string>
    <string name="add_cleanup_rule">添加清理规则</string>
    <string name="add_cleanup_rule_description">添加新的清理规则</string>
    <string name="address_reminder_description">复制地址时，建议打开地图应用</string>
    <string name="address_reminder_message">检测到地址内容，建议打开「%1$s」</string>
    <string name="address_reminder_status_disabled">已关闭</string>
    <string name="address_reminder_status_enabled">正在监控地址复制 - %1$s</string>
    <string name="address_reminder_title">地址提醒</string>
    <string name="advanced_cleanup_strategy">高级清理策略配置</string>
    <string name="advanced_cleanup_strategy_description">自定义清理规则和顺序</string>
    <string name="advanced_memory_config">高级内存配置</string>
    <string name="airplane_mode_changed">飞行模式</string>
    <string name="airplane_mode_changed_description">飞行模式状态变化时触发条件</string>
    <string name="ambient_display">环境显示</string>
    <string name="ambient_display_description">配置环境显示模式</string>
    <string name="app_detection_any_app">任何应用</string>
    <string name="app_detection_selected_apps">指定应用</string>
    <string name="app_execute_javascript">执行JavaScript代码</string>
    <string name="app_execute_javascript_description">执行自定义的JavaScript代码</string>
    <string name="app_execute_shell_script">执行Shell脚本</string>
    <string name="app_execute_shell_script_description">执行自定义的Shell脚本</string>
    <string name="app_force_stop_app">强制停止应用</string>
    <string name="app_force_stop_app_description">强制停止指定的应用程序</string>
    <string name="app_freeze_app">冻结应用</string>
    <string name="app_freeze_app_description">冻结指定的应用程序</string>
    <string name="app_importance_management">应用重要性管理</string>
    <string name="app_importance_management_description">管理应用重要性设置</string>
    <string name="app_interface_click">界面点击</string>
    <string name="app_interface_click_description">检测到对特定文本内容的点击时触发条件</string>
    <string name="app_launch_app">启动应用</string>
    <string name="app_launch_app_description">启动指定的应用程序</string>
    <string name="app_lifecycle">生命周期</string>
    <string name="app_lifecycle_description">应用启动、关闭</string>
    <string name="app_link_reminder_description">检测到应用链接时，智能识别平台并建议打开对应应用</string>
    <string name="app_link_reminder_status_disabled">已关闭</string>
    <string name="app_link_reminder_status_enabled">正在监控剪贴板</string>
    <string name="app_link_reminder_status_unconfigured">功能可直接使用</string>
    <string name="app_link_reminder_title">打开应用链接</string>
    <string name="app_management">应用任务</string>
    <string name="app_management_description">启动、停止、安装、卸载应用</string>
    <string name="app_name">快捷指令</string>
    <string name="app_open_website">打开网站</string>
    <string name="app_open_website_description">在默认浏览器中打开指定网站</string>
    <string name="app_package_management">应用管理</string>
    <string name="app_package_management_description">应用安装、删除、更新</string>
    <string name="app_screen_content">屏幕内容</string>
    <string name="app_screen_content_description">当屏幕出现或移出某些文本内容时触发条件</string>
    <string name="app_selection">选择应用</string>
    <string name="app_state">应用状态</string>
    <string name="app_state_change">状态变化</string>
    <string name="app_state_change_description">应用前台、后台状态变化、后台时间超过阈值</string>
    <string name="app_state_description">应用前台、后台、安装</string>
    <string name="app_tasker_locale_plugin">Tasker/Locale插件</string>
    <string name="app_tasker_locale_plugin_description">执行Tasker或Locale任务插件</string>
    <string name="app_trigger_all_apps">所有应用</string>
    <string name="app_trigger_any_app">任意应用</string>
    <string name="app_unfreeze_app">解冻应用</string>
    <string name="app_unfreeze_app_description">解冻指定的应用程序</string>
    <string name="application_task">应用任务</string>
    <string name="application_task_description">启动应用、强制停止</string>
    <string name="auto_clicker_accessibility_service_description">用于自动点击器功能，包括：录制用户的触摸操作（点击、滑动、长按等）、回放录制的操作序列、管理录制文件（保存、加载、编辑）。如需使用自动点击器相关功能，请启用此服务。</string>
    <string name="auto_clicker_accessibility_service_name">快捷指令-自动点击器服务</string>
    <string name="auto_rotate_disable">禁用</string>
    <string name="auto_rotate_enable">启用</string>
    <string name="auto_sync_state">自动同步</string>
    <string name="auto_sync_state_description">自动同步状态变化时触发条件</string>
    <string name="back">返回</string>
    <string name="batch_delete_confirm">确定要删除选中的 %1$d 个快捷指令吗？此操作将：</string>
    <string name="batch_delete_quick_commands">批量删除快捷指令</string>
    <string name="batch_delete_warning">• 从应用中删除这些快捷指令</string>
    <string name="battery_battery_level">电池电量</string>
    <string name="battery_battery_level_description">监控电池电量变化和阈值</string>
    <string name="battery_charging_state">充电状态</string>
    <string name="battery_charging_state_description">监控设备充电状态变化</string>
    <string name="battery_state">电池状态</string>
    <string name="battery_state_description">电量、充电状态变化</string>
    <string name="camera_flashlight_control">手电筒控制</string>
    <string name="camera_flashlight_control_description">控制手电筒的开启、关闭或切换</string>
    <string name="camera_open_last_photo">打开最后一张照片</string>
    <string name="camera_open_last_photo_description">快速访问相册中最后一张照片</string>
    <string name="camera_record_video">录像</string>
    <string name="camera_record_video_description">使用前置或后置摄像头录像，支持定时录制和自定义保存位置</string>
    <string name="camera_screenshot">截屏</string>
    <string name="camera_screenshot_description">截取当前屏幕内容并保存</string>
    <string name="camera_take_photo">拍照</string>
    <string name="camera_take_photo_description">使用前置或后置摄像头拍照，支持自定义保存位置</string>
    <string name="camera_task">照片查看</string>
    <string name="camera_task_description">查看照片、截屏</string>
    <string name="cancel">取消</string>
    <string name="change_map_apps">更换地图应用</string>
    <string name="change_music_apps">更换音乐应用</string>
    <string name="check_text_content">要检查的文本内容</string>
    <string name="checkup_button">立即体检</string>
    <string name="clipboard_changed">剪贴板变化</string>
    <string name="clipboard_changed_description">当剪贴板内容变化时触发条件</string>
    <string name="comm_call_active">通话中</string>
    <string name="comm_call_active_description">当通话进行中时触发条件</string>
    <string name="comm_call_ended">通话结束</string>
    <string name="comm_call_ended_description">当通话结束时触发条件</string>
    <string name="comm_incoming_call">接到呼叫</string>
    <string name="comm_incoming_call_description">当有来电时触发条件</string>
    <string name="comm_outgoing_call">拨出电话</string>
    <string name="comm_outgoing_call_description">当拨打电话时触发条件</string>
    <string name="comm_sms_received">收到短信</string>
    <string name="comm_sms_received_description">当收到短信时触发条件</string>
    <string name="comm_sms_sent">发送短信</string>
    <string name="comm_sms_sent_description">当发送短信时触发条件</string>
    <string name="communication_state">通信状态</string>
    <string name="communication_state_description">通话、短信状态变化</string>
    <string name="configure_item">配置 %1$s</string>
    <string name="confirm_configuration">确认配置</string>
    <string name="conn_bluetooth_state">蓝牙状态</string>
    <string name="conn_bluetooth_state_description">当蓝牙开启/关闭或连接/断开时触发条件</string>
    <string name="conn_mobile_data">移动数据</string>
    <string name="conn_mobile_data_description">当移动数据连接状态改变时触发条件</string>
    <string name="conn_wifi_network">WiFi网络</string>
    <string name="conn_wifi_network_description">当连接到特定WiFi网络时触发条件</string>
    <string name="conn_wifi_state">WiFi状态</string>
    <string name="conn_wifi_state_description">当WiFi开启/关闭或连接/断开时触发条件</string>
    <string name="connection_state">连接状态</string>
    <string name="connection_state_description">网络、蓝牙、USB连接状态</string>
    <string name="connectivity_airplane_mode_control">飞行模式控制</string>
    <string name="connectivity_airplane_mode_control_description">控制飞行模式的开启、关闭或切换</string>
    <string name="connectivity_bluetooth_control">蓝牙控制</string>
    <string name="connectivity_bluetooth_control_description">控制蓝牙的开启、关闭或切换</string>
    <string name="connectivity_hotspot_control">热点控制</string>
    <string name="connectivity_hotspot_control_description">控制WiFi热点的开启、关闭或切换</string>
    <string name="connectivity_mobile_data_control">移动数据控制</string>
    <string name="connectivity_mobile_data_control_description">控制移动数据的开启、关闭或切换</string>
    <string name="connectivity_network_check">网络连接检查</string>
    <string name="connectivity_network_check_description">检查网络连接状态并返回结果</string>
    <string name="connectivity_nfc_control">NFC控制</string>
    <string name="connectivity_nfc_control_description">控制NFC的开启、关闭或切换</string>
    <string name="connectivity_send_intent">发送Intent</string>
    <string name="connectivity_send_intent_description">发送自定义Intent到指定应用或系统</string>
    <string name="connectivity_task">连接任务</string>
    <string name="connectivity_task_description">WiFi、蓝牙、网络控制</string>
    <string name="connectivity_wifi_control">WiFi控制</string>
    <string name="connectivity_wifi_control_description">控制WiFi的开启、关闭或切换</string>
    <string name="contact_selection">选择联系人</string>
    <string name="custom_app_platform_config">自定义应用平台配置</string>
    <string name="custom_shopping_platform_config">自定义购物平台配置</string>
    <string name="dark_theme_changed">深色主题</string>
    <string name="dark_theme_changed_description">系统深色主题状态变化</string>
    <string name="datetime_alarm">闹钟操作</string>
    <string name="datetime_alarm_description">设置、取消闹钟，支持自定义铃声</string>
    <string name="datetime_stopwatch">秒表操作</string>
    <string name="datetime_stopwatch_description">启动、停止、重置秒表</string>
    <string name="datetime_task">日期时间</string>
    <string name="datetime_task_description">时间设置、闹钟、日历</string>
    <string name="datetime_voice_time_announcement">语音报时</string>
    <string name="datetime_voice_time_announcement_description">使用TTS引擎播报当前时间，支持24/12小时制</string>
    <string name="default_keyboard">键盘-设置默认值</string>
    <string name="default_keyboard_description">设置默认输入法键盘</string>
    <string name="delete">删除</string>
    <string name="delete_operation_irreversible">此操作不可撤销。</string>
    <string name="delete_quick_command">删除快捷指令</string>
    <string name="delete_quick_command_confirm">确定要删除快捷指令"%1$s"吗？此操作将：</string>
    <string name="delete_quick_command_warning">• 从应用中删除此快捷指令</string>
    <string name="demo_mode">演示模式</string>
    <string name="demo_mode_description">控制系统演示模式</string>
    <string name="detailed_configuration">详细配置</string>
    <string name="device_action_share_text">分享文本</string>
    <string name="device_action_share_text_description">通过系统分享功能分享文本内容</string>
    <string name="device_action_task">设备动作</string>
    <string name="device_action_task_description">分享、剪贴板、系统操作</string>
    <string name="device_boot_completed">设备启动</string>
    <string name="device_boot_completed_description">设备启动完成时触发条件</string>
    <string name="device_clipboard_changed">剪贴板变化</string>
    <string name="device_clipboard_changed_description">当剪贴板内容变化时触发条件</string>
    <string name="device_event">设备事件</string>
    <string name="device_event_description">电池、屏幕、系统事件</string>
    <string name="device_gps_state">GPS状态</string>
    <string name="device_gps_state_description">当GPS状态改变时触发条件</string>
    <string name="device_logcat_message">Logcat消息</string>
    <string name="device_logcat_message_description">当Logcat出现指定消息时触发条件</string>
    <string name="device_settings">设备设置</string>
    <string name="device_settings_accessibility_service">无障碍服务</string>
    <string name="device_settings_accessibility_service_description">控制指定无障碍服务的启用状态</string>
    <string name="device_settings_auto_rotate">屏幕自动旋转</string>
    <string name="device_settings_auto_rotate_description">控制屏幕自动旋转功能</string>
    <string name="device_settings_description">系统设置、主题、壁纸、反色等设备级配置</string>
    <string name="device_settings_display_density">显示密度</string>
    <string name="device_settings_display_density_description">调整屏幕显示密度百分比</string>
    <string name="device_settings_driving_mode">驾驶模式</string>
    <string name="device_settings_driving_mode_description">控制驾驶模式功能</string>
    <string name="device_settings_enter_screensaver">进入屏保模式</string>
    <string name="device_settings_enter_screensaver_description">激活系统屏保模式</string>
    <string name="device_settings_font_size">字体大小</string>
    <string name="device_settings_font_size_description">调整系统字体大小百分比</string>
    <string name="device_settings_immersive_mode">沉浸模式</string>
    <string name="device_settings_immersive_mode_description">设置系统沉浸模式类型</string>
    <string name="device_settings_invert_colors">反色</string>
    <string name="device_settings_invert_colors_description">切换屏幕反色显示模式</string>
    <string name="device_settings_keyboard_hint">键盘提示</string>
    <string name="device_settings_keyboard_hint_description">控制键盘提示功能</string>
    <string name="dialog_content">对话框内容</string>
    <string name="dialog_content_placeholder">输入对话框内容</string>
    <string name="dialog_settings">显示对话框设置</string>
    <string name="dialog_title">对话框标题</string>
    <string name="dialog_title_placeholder">输入对话框标题</string>
    <string name="digital_assistant">设置数字助理</string>
    <string name="digital_assistant_description">设置默认数字助理应用</string>
    <string name="disable">关闭</string>
    <string name="dock_state">底座连接</string>
    <string name="dock_state_description">设备底座连接状态变化</string>
    <string name="driving_mode">驾驶模式</string>
    <string name="driving_mode_description">控制驾驶模式功能</string>
    <string name="edit_abort_condition">编辑中止条件</string>
    <string name="edit_abort_condition_description">修改中止条件参数</string>
    <string name="edit_condition">编辑条件</string>
    <string name="edit_condition_description">修改触发条件参数</string>
    <string name="edit_task">编辑任务</string>
    <string name="edit_task_description">修改任务参数</string>
    <string name="enable">开启</string>
    <string name="executing_quick_command">正在执行快捷指令：%1$s（%2$d个任务）</string>
    <string name="execution_mode">执行模式</string>
    <string name="experimental_features">实验性功能</string>
    <string name="experimental_features_description">启用实验性功能可能会影响应用稳定性，请谨慎使用</string>
    <string name="experimental_features_enabled">启用实验性功能</string>
    <string name="file_file_operation">文件管理</string>
    <string name="file_file_operation_description">文件管理操作：复制、移动、删除、压缩、新建文件夹、重命名</string>
    <string name="file_open_file">打开文件</string>
    <string name="file_open_file_description">打开指定文件，支持手动输入或选择文件，可指定应用</string>
    <string name="file_operation_task">文件读写</string>
    <string name="file_operation_task_description">文件读写、打开文件</string>
    <string name="file_write_file">写入文件</string>
    <string name="file_write_file_description">向指定文件写入内容，支持添加、覆盖、准备提交模式</string>
    <string name="flashlight_reminder_description">手电筒开启后，建议关闭</string>
    <string name="flashlight_reminder_status_disabled">已关闭</string>
    <string name="flashlight_reminder_status_enabled">正在监控手电筒状态</string>
    <string name="flashlight_reminder_status_unconfigured">功能可直接使用</string>
    <string name="flashlight_reminder_title">手电筒提醒</string>
    <string name="font_size">字体大小</string>
    <string name="font_size_description">调整系统字体大小百分比</string>
    <string name="font_size_percentage">字体大小百分比</string>
    <string name="font_size_settings">字体大小设置</string>
    <string name="font_weight_bold">粗体</string>
    <string name="font_weight_medium">中等</string>
    <string name="font_weight_regular">常规</string>
    <string name="font_weight_selection_title">选择字重</string>
    <string name="gesture_recognition_accessibility_service_description">用于手势识别，包括：指纹手势触发条件（当您做出指定指纹手势时触发条件）。如需使用手势相关的触发条件，请启用此服务。</string>
    <string name="gesture_recognition_accessibility_service_name">快捷指令-手势识别服务</string>
    <string name="gesture_recording_edit">编辑手势录制</string>
    <string name="gesture_recording_edit_description">编辑已录制的手势操作</string>
    <string name="go_to_settings">前往设置</string>
    <string name="gps_state">GPS状态</string>
    <string name="gps_state_description">当GPS状态改变时触发条件</string>
    <string name="icon_weight_bold">粗体</string>
    <string name="icon_weight_medium">中黑体</string>
    <string name="icon_weight_regular">常规体</string>
    <string name="icon_weight_selection_title">选择图标粗细</string>
    <string name="info_message_ringtone">信息铃声设置</string>
    <string name="info_message_ringtone_description">设置短信和通知的铃声</string>
    <string name="info_send_email">发送邮件</string>
    <string name="info_send_email_description">发送邮件到指定邮箱，支持完整的SMTP配置和通知选项</string>
    <string name="info_send_sms">发送短信</string>
    <string name="info_send_sms_description">发送短信到指定号码，支持SIM卡选择和预填写模式</string>
    <string name="info_show_dialog">显示对话框</string>
    <string name="info_show_dialog_description">显示自定义对话框，支持标题、内容和按钮配置</string>
    <string name="info_show_toast">显示Toast</string>
    <string name="info_show_toast_description">在屏幕上显示短暂的提示消息</string>
    <string name="information_task">信息任务</string>
    <string name="information_task_description">短信、邮件、通知</string>
    <string name="intelligent_link_recognition_description">自动优化链接格式，提升识别准确性和兼容性</string>
    <string name="intelligent_link_recognition_enabled">启用智能识别</string>
    <string name="intelligent_link_recognition_help">自动标准化链接格式，清理非标准字符和干扰内容</string>
    <string name="intelligent_link_recognition_title">智能链接识别</string>
    <string name="intent_received">Intent接收</string>
    <string name="intent_received_description">接收到指定Intent时触发条件</string>
    <string name="interface_click">界面点击</string>
    <string name="interface_click_description">检测到对特定文本内容的点击时触发条件</string>
    <string name="interface_interaction_accessibility_service_description">用于界面自动化操作，包括：界面点击条件（检测到对特定文本内容的点击时触发条件）、屏幕内容条件（当屏幕出现或移出某些文本内容时触发条件）、检查屏幕文字任务（检查特定文本字符串当前是否显示在屏幕上，支持精确匹配/包含匹配、叠加层检测、视图ID提取等高级选项）、读取屏幕内容任务（将当前屏幕的内容捕获到文本文件中）、检查界面元素颜色任务（分析指定位置的界面元素颜色特征，基于元素类型、状态、文本内容等推断颜色信息）。如需使用界面相关的触发条件或任务，请启用此服务。</string>
    <string name="interface_interaction_accessibility_service_name">快捷指令-界面交互服务</string>
    <string name="interface_interaction_service_not_enabled">界面交互无障碍服务未启用，无法执行检查屏幕文字任务</string>
    <string name="invalid_command">无效的指令</string>
    <string name="invert_colors">反色</string>
    <string name="invert_colors_description">切换屏幕反色显示模式</string>
    <string name="invert_colors_off">关闭</string>
    <string name="invert_colors_on">开启</string>
    <string name="invert_colors_operation">反色操作</string>
    <string name="keyboard_hint">键盘提示</string>
    <string name="keyboard_hint_description">控制键盘提示功能</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_selection_title">选择语言</string>
    <string name="language_settings">语言设置</string>
    <string name="language_settings_description">选择应用显示语言</string>
    <string name="language_system_default">系统默认</string>
    <string name="lifecycle">生命周期</string>
    <string name="lifecycle_description">应用启动、关闭</string>
    <string name="location_force_location_update">强制位置更新</string>
    <string name="location_force_location_update_description">强制更新当前位置信息</string>
    <string name="location_get_location">获取位置</string>
    <string name="location_get_location_description">获取当前位置信息并保存到变量</string>
    <string name="location_set_location_update_frequency">设置位置更新频率</string>
    <string name="location_set_location_update_frequency_description">设置位置信息的更新频率</string>
    <string name="location_share_location">分享位置</string>
    <string name="location_share_location_description">通过短信、联系人或手机号码分享当前位置</string>
    <string name="location_task">位置任务</string>
    <string name="location_task_description">GPS、位置服务控制</string>
    <string name="location_toggle_location_service">定位服务控制</string>
    <string name="location_toggle_location_service_description">开启、关闭或切换定位服务状态</string>
    <string name="logcat_message">Logcat消息</string>
    <string name="logcat_message_description">当Logcat出现指定消息时触发条件</string>
    <string name="login_attempt_failed">登录失败</string>
    <string name="login_attempt_failed_description">设备登录尝试失败时触发条件</string>
    <string name="manual_dynamic_shortcut">动态快捷方式</string>
    <string name="manual_dynamic_shortcut_description">通过动态快捷方式触发</string>
    <string name="manual_fingerprint_gesture">指纹手势</string>
    <string name="manual_fingerprint_gesture_description">通过指纹手势触发</string>
    <string name="manual_home_button_long_press">主屏幕按钮长按</string>
    <string name="manual_home_button_long_press_description">当你长时间按住主页键时,将触发条件。这个条件可能需要将本应用配置为默认的辅助和语音输入应用程序，可能需要申请录音权限，以便应用可持续正常运行。</string>
    <string name="manual_media_key_press">媒体键按下</string>
    <string name="manual_media_key_press_description">当媒体键(在耳机上)被按下1到3次时，将触发条件</string>
    <string name="manual_static_shortcut">静态快捷方式</string>
    <string name="manual_static_shortcut_description">通过静态快捷方式触发</string>
    <string name="manual_trigger">手动触发</string>
    <string name="manual_trigger_description">快捷方式、小组件触发</string>
    <string name="manual_volume_key_press">音量键按下</string>
    <string name="manual_volume_key_press_description">通过音量键按下触发，可选择是否保留原音量</string>
    <string name="manual_widget_update">手动更新小组件</string>
    <string name="manual_widget_update_description">立即更新所有桌面小组件内容，无论是否启用自动更新</string>
    <string name="match_options">匹配选项</string>
    <string name="media_microphone_recording">麦克风录音</string>
    <string name="media_microphone_recording_description">录制音频，支持自定义时长和格式</string>
    <string name="media_multimedia_control">多媒体控制</string>
    <string name="media_multimedia_control_description">模拟媒体按钮、默认播放器控制、音频按钮</string>
    <string name="media_play_stop_sound">播放/停止声音</string>
    <string name="media_play_stop_sound_description">播放音频文件、铃声或停止现有声音</string>
    <string name="media_task">媒体任务</string>
    <string name="media_task_description">媒体控制、声音播放</string>
    <string name="memory_learning_data">内存学习数据</string>
    <string name="music_app_reminder_description">连接耳机时，建议打开音乐应用</string>
    <string name="music_app_reminder_status_configured">已选择：%s</string>
    <string name="music_app_reminder_status_disabled">已关闭</string>
    <string name="music_app_reminder_status_enabled">耳机连接时提醒打开：%s</string>
    <string name="music_app_reminder_status_unconfigured">需要选择音乐应用</string>
    <string name="music_app_reminder_title">音乐应用提醒</string>
    <string name="music_playback_state">音乐播放</string>
    <string name="music_playback_state_description">音乐播放状态变化时触发条件</string>
    <string name="nav_command_templates">探索</string>
    <string name="nav_global_settings">设置</string>
    <string name="nav_phone_checkup">体检</string>
    <string name="nav_quick_commands">指令</string>
    <string name="nav_smart_reminders">提醒</string>
    <string name="new_app_reminder_description">安装新应用后，建议打开</string>
    <string name="new_app_reminder_status_disabled">已关闭</string>
    <string name="new_app_reminder_status_enabled">正在监控应用安装</string>
    <string name="new_app_reminder_status_unconfigured">功能可直接使用</string>
    <string name="new_app_reminder_title">打开新应用</string>
    <string name="no_quick_commands">没有快捷指令</string>
    <string name="no_search_results">未找到匹配的快捷指令</string>
    <string name="no_template_search_results">未找到匹配的模板</string>
    <string name="no_templates">暂无可用模板</string>
    <string name="notification_cancel_notification">取消通知</string>
    <string name="notification_cancel_notification_description">取消指定的通知</string>
    <string name="notification_event">通知事件</string>
    <string name="notification_event_description">通知发布、取消时触发条件</string>
    <string name="notification_listener_service_description">用于监听系统通知事件，实现通知相关的自动化功能。</string>
    <string name="notification_listener_service_name">快捷指令 - 通知监听</string>
    <string name="notification_show_notification">显示通知</string>
    <string name="notification_show_notification_description">在通知栏显示自定义通知</string>
    <string name="notification_task">通知任务</string>
    <string name="notification_task_description">发送、取消通知</string>
    <string name="optimization_complete">优化完成</string>
    <string name="optimization_failed">优化失败</string>
    <string name="optimize_button">一键优化</string>
    <string name="optimizing">正在优化...</string>
    <string name="package_management">应用管理</string>
    <string name="package_management_description">应用安装、删除、更新</string>
    <string name="package_name">包名</string>
    <string name="phone_answer_call">接听电话</string>
    <string name="phone_answer_call_description">接听当前来电</string>
    <string name="phone_checkup_title">体检</string>
    <string name="phone_clear_call_log">清除通话记录</string>
    <string name="phone_clear_call_log_description">清除指定类型的通话记录</string>
    <string name="phone_make_call">拨打电话</string>
    <string name="phone_make_call_description">拨打指定电话号码</string>
    <string name="phone_open_call_log">打开通话记录</string>
    <string name="phone_open_call_log_description">打开系统通话记录页面</string>
    <string name="phone_reject_call">拒接电话</string>
    <string name="phone_reject_call_description">拒接当前来电</string>
    <string name="phone_ringtone_settings">电话铃声设置</string>
    <string name="phone_ringtone_settings_description">设置电话来电铃声，选择自定义铃声并应用到系统</string>
    <string name="phone_status_excellent">手机运行良好，性能优秀</string>
    <string name="phone_status_good">手机运行正常，建议优化</string>
    <string name="phone_status_poor">手机运行缓慢，需要优化</string>
    <string name="phone_task">电话任务</string>
    <string name="phone_task_description">拨打电话、接听电话等</string>
    <string name="power_save_mode">省电模式</string>
    <string name="power_save_mode_description">控制系统省电模式</string>
    <string name="quick_command_aborted">快捷指令执行被中止：%1$s，中止条件：%2$s</string>
    <string name="quick_command_completed">快捷指令执行完成：%1$s</string>
    <string name="quick_command_edit">编辑快捷指令</string>
    <string name="quick_command_form">快捷指令表单</string>
    <string name="quick_command_new">新建快捷指令</string>
    <string name="quick_command_not_found">找不到关联的快捷指令</string>
    <string name="quick_command_not_found_simple">找不到快捷指令</string>
    <string name="quick_commands_title">指令</string>
    <string name="ringer_mode_changed">铃声模式</string>
    <string name="ringer_mode_changed_description">铃声模式变化时触发条件</string>
    <string name="ringtone_selection">选择铃声</string>
    <string name="running_apps_count">后台运行应用: %d个</string>
    <string name="save">保存</string>
    <string name="screen_brightness_control">亮度控制</string>
    <string name="screen_brightness_control_description">调节屏幕亮度，支持百分比和绝对值设置</string>
    <string name="screen_content">屏幕内容</string>
    <string name="screen_content_description">当屏幕出现或移出某些文本内容时触发条件</string>
    <string name="screen_content_text">屏幕内容文本</string>
    <string name="screen_content_text_label">要检测的屏幕文本内容</string>
    <string name="screen_content_text_placeholder">例如：加载中、网络错误、登录成功等</string>
    <string name="screen_control_task">屏幕控制</string>
    <string name="screen_control_task_description">屏幕亮度、方向、锁屏</string>
    <string name="screen_event_auto_rotate_disabled">自动旋转禁用</string>
    <string name="screen_event_auto_rotate_enabled">自动旋转启用</string>
    <string name="screen_event_off">屏幕关闭</string>
    <string name="screen_event_on">屏幕开启</string>
    <string name="screen_event_unlocked">屏幕解锁</string>
    <string name="screen_keep_device_awake">保持设备唤醒</string>
    <string name="screen_keep_device_awake_description">防止设备进入休眠状态</string>
    <string name="screen_rotation_reminder_description">翻转手机后，建议屏幕旋转</string>
    <string name="screen_rotation_reminder_status_disabled">已关闭</string>
    <string name="screen_rotation_reminder_status_enabled">正在监控屏幕旋转</string>
    <string name="screen_rotation_reminder_status_unconfigured">功能可直接使用</string>
    <string name="screen_rotation_reminder_title">屏幕旋转提醒</string>
    <string name="screen_state">屏幕状态</string>
    <string name="screen_state_description">屏幕开启、关闭、解锁状态变化</string>
    <string name="screen_text_check_failed">检查屏幕文字失败</string>
    <string name="search_apps">搜索应用名称或包名</string>
    <string name="search_field_icon_weight">搜索图标粗细</string>
    <string name="search_field_icon_weight_description">调整搜索框图标的粗细程度</string>
    <string name="search_field_placeholder_font_weight">提示文字字重</string>
    <string name="search_field_placeholder_font_weight_description">调整搜索框提示文字的粗细程度</string>
    <string name="search_field_settings">搜索框设置</string>
    <string name="search_quick_commands">搜索快捷指令名称</string>
    <string name="search_smart_reminders">搜索智慧提醒功能</string>
    <string name="search_templates">搜索模板名称或描述</string>
    <string name="select_map_apps">选择地图应用</string>
    <string name="select_music_apps">选择音乐应用</string>
    <string name="sensor_activity_recognition">运动识别</string>
    <string name="sensor_activity_recognition_description">当检测到特定运动状态时触发条件</string>
    <string name="sensor_flip_sensor">设备翻转检测</string>
    <string name="sensor_flip_sensor_description">当设备翻转时触发条件</string>
    <string name="sensor_light_sensor">光线传感器</string>
    <string name="sensor_light_sensor_description">当环境光线变化时触发条件</string>
    <string name="sensor_orientation_sensor">屏幕方向传感器</string>
    <string name="sensor_orientation_sensor_description">当屏幕方向变化时触发条件</string>
    <string name="sensor_proximity_sensor">接近传感器</string>
    <string name="sensor_proximity_sensor_description">当物体接近或远离时触发条件</string>
    <string name="sensor_shake_sensor">摇晃检测</string>
    <string name="sensor_shake_sensor_description">当设备摇晃时触发条件</string>
    <string name="sensor_sleep_sensor">睡眠检测</string>
    <string name="sensor_sleep_sensor_description">当检测到睡眠状态变化时触发条件</string>
    <string name="sensor_state">传感器状态</string>
    <string name="sensor_state_description">光线、方向、震动传感器</string>
    <string name="share_target_selection">选择分享方式</string>
    <string name="share_text">分享文本</string>
    <string name="share_text_description">通过系统分享功能分享文本内容</string>
    <string name="share_url_reminder_description">复制网址链接时，建议分享网址</string>
    <string name="share_url_reminder_status_disabled">已关闭</string>
    <string name="share_url_reminder_status_enabled">正在监控剪贴板</string>
    <string name="share_url_reminder_status_unconfigured">功能可直接使用</string>
    <string name="share_url_reminder_title">分享网址</string>
    <string name="shell_script">Shell脚本</string>
    <string name="shell_script_placeholder">输入要执行的Shell脚本</string>
    <string name="shell_script_placeholder_command">输入要执行的Shell命令或脚本</string>
    <string name="shell_script_settings">Shell脚本设置</string>
    <string name="shizuku_category_app_management">应用管理命令</string>
    <string name="shizuku_category_key_simulation">按键模拟命令</string>
    <string name="shizuku_category_network">网络相关命令</string>
    <string name="shizuku_category_screen_operation">屏幕操作命令</string>
    <string name="shizuku_category_system">系统控制命令</string>
    <string name="shizuku_install_guide">请先安装并启动 Shizuku 应用</string>
    <string name="shizuku_not_installed">未安装 Shizuku</string>
    <string name="shizuku_not_running">Shizuku 未运行</string>
    <string name="shizuku_permission_required">需要 Shizuku 权限</string>
    <string name="shopping_app_reminder_description">复制商品链接时，建议打开对应购物应用</string>
    <string name="shopping_app_reminder_status_disabled">已关闭</string>
    <string name="shopping_app_reminder_status_enabled">正在监控剪贴板</string>
    <string name="shopping_app_reminder_status_unconfigured">功能可直接使用</string>
    <string name="shopping_app_reminder_title">购物应用提醒</string>
    <string name="shortcut_1_long_label">快捷指令1</string>
    <string name="shortcut_1_short_label">快捷指令1</string>
    <string name="shortcut_2_long_label">快捷指令2</string>
    <string name="shortcut_2_short_label">快捷指令2</string>
    <string name="shortcut_3_long_label">快捷指令3</string>
    <string name="shortcut_3_short_label">快捷指令3</string>
    <string name="shortcut_4_long_label">快捷指令4</string>
    <string name="shortcut_4_short_label">快捷指令4</string>
    <string name="shortcut_not_configured">此快捷方式尚未配置，请在应用中创建快捷指令并选择使用此静态快捷方式</string>
    <string name="sim_card_state">SIM卡状态</string>
    <string name="sim_card_state_description">SIM卡状态变化时触发条件</string>
    <string name="smart_reminder_change_app">更换应用</string>
    <string name="smart_reminder_config_error">配置错误</string>
    <string name="smart_reminder_config_not_found">找不到对应的配置项</string>
    <string name="smart_reminder_configure">配置</string>
    <string name="smart_reminder_configured">已配置</string>
    <string name="smart_reminder_detail_config">智慧提醒详细配置</string>
    <string name="smart_reminder_detail_settings">详细设置</string>
    <string name="smart_reminder_needs_configuration">需要完成配置</string>
    <string name="smart_reminder_ready_to_use">可直接使用</string>
    <string name="smart_reminder_select_app">选择应用</string>
    <string name="smart_reminder_selected_apps_count">已选择 %d 个应用</string>
    <string name="smart_reminder_status_label">状态：</string>
    <string name="smart_reminder_tap_to_setup">轻触设置</string>
    <string name="smart_reminder_type_not_found">找不到指定的智慧提醒类型</string>
    <string name="smart_reminders_description">智能检测并提供有用的提醒</string>
    <string name="smart_reminders_title">智慧提醒</string>
    <string name="state_change">状态变化</string>
    <string name="state_change_description">应用前台、后台状态变化、后台时间超过阈值</string>
    <string name="stopwatch_selection">选择秒表</string>
    <string name="storage_permission_description">导出日志需要存储权限，请在设置中授予权限</string>
    <string name="storage_permission_required">需要存储权限才能导出日志</string>
    <string name="sun_event_sunrise">日出</string>
    <string name="sun_event_sunset">日落</string>
    <string name="switch_operation_off">关闭</string>
    <string name="switch_operation_on">开启</string>
    <string name="switch_operation_toggle">切换</string>
    <string name="system_operation_accessibility_service_description">用于执行系统级操作，包括：快速设置面板、电源菜单、最近任务、应用抽屉、无障碍功能切换、按返回键、屏幕开关控制、主屏幕按钮长按检测、媒体键按下检测等。如需使用系统操作或手动触发相关功能，请启用此服务。</string>
    <string name="system_operation_accessibility_service_name">快捷指令-系统操作服务</string>
    <string name="system_setting_changed">系统设置</string>
    <string name="system_setting_changed_description">系统设置项变化时触发条件</string>
    <string name="system_settings">系统设置</string>
    <string name="system_settings_description">修改系统设置表中的键值对</string>
    <string name="tap_to_select_map_apps">轻触选择要监控的地图应用</string>
    <string name="tap_to_select_music_apps">轻触选择要监控的音乐应用</string>
    <string name="task_alarm_reminder">闹钟提醒</string>
    <string name="task_alarm_reminder_description">设置闹钟和提醒</string>
    <string name="task_app_management">应用任务</string>
    <string name="task_app_management_description">启动、停止、安装、卸载应用</string>
    <string name="task_device_settings">设备设置</string>
    <string name="task_device_settings_description">系统设置、主题、壁纸、反色等设备级配置</string>
    <string name="task_file_operation">文件操作</string>
    <string name="task_file_operation_description">创建、删除、移动、复制文件</string>
    <string name="task_location">位置任务</string>
    <string name="task_location_description">获取位置信息、地理围栏</string>
    <string name="task_log">日志任务</string>
    <string name="task_log_description">记录日志信息</string>
    <string name="task_log_task">日志任务</string>
    <string name="task_log_task_description">记录日志信息</string>
    <string name="task_media_task">媒体任务</string>
    <string name="task_media_task_description">播放音频、录制音频、拍照、录像</string>
    <string name="task_network">网络任务</string>
    <string name="task_network_description">HTTP请求、网络检测</string>
    <string name="task_phone">电话任务</string>
    <string name="task_phone_description">拨打电话、接听电话、通话记录管理</string>
    <string name="task_phone_task">电话任务</string>
    <string name="task_phone_task_description">拨打电话、接听电话、通话记录管理</string>
    <string name="task_screen_control">屏幕控制</string>
    <string name="task_screen_control_description">亮度控制、屏幕开关、旋转控制等</string>
    <string name="task_screen_control_task">屏幕控制</string>
    <string name="task_screen_control_task_description">亮度控制、屏幕开关、旋转控制等</string>
    <string name="tasker_locale_plugin">Tasker/Locale插件</string>
    <string name="tasker_locale_plugin_description">监听Tasker/Locale插件状态</string>
    <string name="tasks_count">%1$d个任务</string>
    <string name="template_anti_embarrassment_mode_desc">避免打开短视频软件时音量过大带来的尴尬</string>
    <string name="template_anti_embarrassment_mode_title">防社死模式</string>
    <string name="template_auto_brightness_desc">根据环境光线自动调节屏幕亮度</string>
    <string name="template_auto_brightness_title">自动亮度调节</string>
    <string name="template_battery_saver_desc">电量低于阈值时自动开启省电模式</string>
    <string name="template_battery_saver_title">省电模式</string>
    <string name="template_category_automation">自动化</string>
    <string name="template_category_display">显示控制</string>
    <string name="template_category_network">网络管理</string>
    <string name="template_category_power">电源管理</string>
    <string name="template_category_system">系统操作</string>
    <string name="template_do_not_disturb_desc">在指定时间段自动开启免打扰模式</string>
    <string name="template_do_not_disturb_title">免打扰模式</string>
    <string name="template_screen_off_network_desc">屏幕关闭后延时关闭WiFi和移动数据，节省电量</string>
    <string name="template_screen_off_network_title">息屏关闭网络</string>
    <string name="template_screen_on_network_desc">屏幕打开后延时恢复之前保存的网络状态</string>
    <string name="template_screen_on_network_title">亮屏恢复网络</string>
    <string name="text_content_label">文本内容</string>
    <string name="text_content_placeholder">例如：登录成功、加载完成等</string>
    <string name="time_based">时间条件</string>
    <string name="time_based_description">定时、周期、延迟触发</string>
    <string name="time_condition_delayed_trigger">延迟触发</string>
    <string name="time_condition_delayed_trigger_description">延迟指定时间后触发一次</string>
    <string name="time_condition_periodic_time">周期时间</string>
    <string name="time_condition_periodic_time_description">在每周指定的时间重复触发</string>
    <string name="time_condition_periodic_trigger">周期触发</string>
    <string name="time_condition_scheduled_time">日程时间</string>
    <string name="time_condition_scheduled_time_description">在指定的日期和时间触发</string>
    <string name="time_condition_stopwatch">秒表</string>
    <string name="time_condition_stopwatch_description">设定倒计时时间，时间到达时触发</string>
    <string name="time_condition_sun_event">日出日落</string>
    <string name="time_condition_sun_event_description">在日出或日落时间触发</string>
    <string name="time_condition_time_period">时间段</string>
    <string name="time_condition_time_period_description">在指定的时间段内触发</string>
    <string name="toggle">切换</string>
    <string name="trigger_mode">触发模式</string>
    <string name="unified_configuration">统一配置选择</string>
    <string name="update_now">立即更新</string>
    <string name="usage_stats_permission_description">检测应用运行状态需要此权限，请在设置中授予权限</string>
    <string name="usage_stats_permission_required">需要使用情况访问权限</string>
    <string name="volume_changed">音量变化</string>
    <string name="volume_changed_description">系统音量变化时触发条件</string>
    <string name="volume_do_not_disturb">勿扰模式</string>
    <string name="volume_do_not_disturb_description">设置勿扰模式状态</string>
    <string name="volume_speakerphone_control">免提通话控制</string>
    <string name="volume_speakerphone_control_description">控制免提通话的开启、关闭或切换</string>
    <string name="volume_task">音量任务</string>
    <string name="volume_task_description">音量调节、静音控制</string>
    <string name="volume_vibration_mode">振动模式</string>
    <string name="volume_vibration_mode_description">设置设备的振动模式</string>
    <string name="volume_volume_adjust">音量调节</string>
    <string name="volume_volume_adjust_description">增加或减少指定音频流的音量</string>
    <string name="volume_volume_change">音量变化</string>
    <string name="volume_volume_change_description">设置指定音频流的音量大小</string>
    <string name="volume_volume_popup">音量弹出窗口</string>
    <string name="volume_volume_popup_description">显示系统音量调节弹出窗口</string>
    <string name="widget_1_label">快捷指令1</string>
    <string name="widget_2_label">快捷指令2</string>
    <string name="widget_3_label">快捷指令3</string>
    <string name="widget_4_label">快捷指令4</string>
    <string name="widget_update_completed">小组件更新完成</string>
    <string name="widget_update_enabled">启用小组件更新</string>
    <string name="widget_update_enabled_description">定期更新桌面小组件内容，有助于提高应用存活率，但会消耗更多电量</string>
    <string name="widget_update_interval">更新间隔</string>
    <string name="widget_update_interval_error">请输入有效的数字（最小值为1）</string>
    <string name="widget_update_interval_hint">建议设置为6-24小时，过于频繁会影响电池续航</string>
    <string name="widget_update_settings">小组件更新设置</string>
    <style name="Theme.QuickCommands" parent="android:Theme.Material.Light.NoActionBar"/>
    <style name="Theme.QuickCommands.NoActionBar" parent="Theme.QuickCommands">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
</resources>
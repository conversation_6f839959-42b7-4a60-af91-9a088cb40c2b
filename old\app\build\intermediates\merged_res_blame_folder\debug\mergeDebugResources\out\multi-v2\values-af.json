{"logs": [{"outputFile": "com.weinuo.quickcommands.app-mergeDebugResources-46:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3e109b7321dece0f02b118f7abb21702\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180,276,363,460,561,647,723,814,904,990,1068,1149,1220,1309,1384,1455,1526,1607,1677", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,88,74,70,70,80,69,119", "endOffsets": "271,358,455,556,642,718,809,899,985,1063,1144,1215,1304,1379,1450,1521,1602,1672,1792"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "911,1007,1094,1191,1292,1378,1454,7721,7811,7897,7975,8056,8127,8216,8291,8362,8534,8615,8685", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,88,74,70,70,80,69,119", "endOffsets": "1002,1089,1186,1287,1373,1449,1540,7806,7892,7970,8051,8122,8211,8286,8357,8428,8610,8680,8800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bb17b206e660e96f6900b88209c2dff8\\transformed\\core-1.16.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "179,277,379,477,575,682,791,8433", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "272,374,472,570,677,786,906,8529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f503a90f72cbab575267a43a3d872ea8\\transformed\\foundation-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,216", "endColumns": "73,86,84", "endOffsets": "124,211,296"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8805,8892", "endColumns": "73,86,84", "endOffsets": "174,8887,8972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ad2279977a0ab9a9faf3d3cd6fc86c57\\transformed\\material3-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,290,405,521,621,725,846,987,1115,1257,1342,1441,1531,1627,1742,1863,1967,2095,2220,2352,2518,2643,2765,2888,3017,3108,3207,3323,3449,3549,3659,3762,3899,4039,4145,4243,4320,4414,4508,4612,4697,4785,4890,4971,5054,5153,5251,5346,5444,5532,5635,5735,5838,5954,6035,6135", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "167,285,400,516,616,720,841,982,1110,1252,1337,1436,1526,1622,1737,1858,1962,2090,2215,2347,2513,2638,2760,2883,3012,3103,3202,3318,3444,3544,3654,3757,3894,4034,4140,4238,4315,4409,4503,4607,4692,4780,4885,4966,5049,5148,5246,5341,5439,5527,5630,5730,5833,5949,6030,6130,6226"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1545,1662,1780,1895,2011,2111,2215,2336,2477,2605,2747,2832,2931,3021,3117,3232,3353,3457,3585,3710,3842,4008,4133,4255,4378,4507,4598,4697,4813,4939,5039,5149,5252,5389,5529,5635,5733,5810,5904,5998,6102,6187,6275,6380,6461,6544,6643,6741,6836,6934,7022,7125,7225,7328,7444,7525,7625", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "1657,1775,1890,2006,2106,2210,2331,2472,2600,2742,2827,2926,3016,3112,3227,3348,3452,3580,3705,3837,4003,4128,4250,4373,4502,4593,4692,4808,4934,5034,5144,5247,5384,5524,5630,5728,5805,5899,5993,6097,6182,6270,6375,6456,6539,6638,6736,6831,6929,7017,7120,7220,7323,7439,7520,7620,7716"}}]}]}
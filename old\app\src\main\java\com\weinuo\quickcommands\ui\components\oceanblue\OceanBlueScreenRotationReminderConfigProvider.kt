package com.weinuo.quickcommands.ui.components.oceanblue

import android.content.Context
import androidx.compose.foundation.layout.*

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.SmartReminderType
import com.weinuo.quickcommands.service.QuickCommandsService
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem

/**
 * 屏幕旋转提醒配置提供者
 *
 * 按照快捷指令配置架构模式设计，提供屏幕旋转提醒功能的配置项。
 * 遵循快捷指令Provider命名规则：具体功能名+ConfigProvider。
 *
 * 设计原则：
 * - 遵循快捷指令的Provider模式（具体功能名称）
 * - 使用ConfigurationCardItem统一配置项结构
 * - 支持编辑模式和新建模式
 * - 专注于屏幕旋转提醒功能
 */
object OceanBlueScreenRotationReminderConfigProvider {

    /**
     * 获取屏幕旋转提醒配置项
     */
    fun getConfigurationItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return ConfigurationCardItem(
            id = "screen_rotation_reminder",
            title = context.getString(R.string.screen_rotation_reminder_title),
            description = context.getString(R.string.screen_rotation_reminder_description),
            operationType = SmartReminderType.SCREEN_ROTATION_REMINDER,
            permissionRequired = true, // 屏幕旋转提醒需要悬浮窗权限
            content = { reminderType, onComplete ->
                ScreenRotationReminderConfigContent(reminderType, onComplete)
            },
            editableContent = { reminderType, initialConfig, onComplete ->
                ScreenRotationReminderConfigContent(reminderType, onComplete, initialConfig)
            }
        )
    }

    /**
     * 获取配置内容组件（支持外部保存请求）
     *
     * 提供屏幕旋转提醒的配置内容组件，用于在智慧提醒详细配置界面中显示。
     * 支持外部保存请求机制，用于右上角保存按钮。
     *
     * @param reminderType 智慧提醒类型
     * @param onComplete 配置完成回调
     * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
     */
    @Composable
    fun getConfigContent(
        reminderType: SmartReminderType,
        onComplete: (Any) -> Unit,
        onSaveRequested: ((suspend () -> Unit) -> Unit)? = null
    ) {
        ScreenRotationReminderConfigContent(
            reminderType = reminderType,
            onComplete = onComplete,
            onSaveRequested = onSaveRequested
        )
    }
}

/**
 * 屏幕旋转提醒配置内容组件
 *
 * 提供完整的屏幕旋转提醒配置选项：
 * - 检测灵敏度设置（15-75度）
 * - 提醒间隔配置（10-300秒）
 * - 延迟检测时间（1-10秒）
 * - 自动消失设置（1-60秒）
 * - 按钮显示配置（位置、尺寸、边距）
 *
 * @param reminderType 智慧提醒类型
 * @param onComplete 配置完成回调
 * @param initialConfig 初始配置（编辑模式使用）
 * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
 */
@Composable
private fun ScreenRotationReminderConfigContent(
    reminderType: SmartReminderType,
    onComplete: (Any) -> Unit,
    initialConfig: Any? = null,
    onSaveRequested: ((suspend () -> Unit) -> Unit)? = null
) {
    val context = LocalContext.current
    val configAdapter = remember { SmartReminderConfigAdapter(context) }

    // 配置参数状态
    var sensitivity by remember { mutableStateOf("45") } // 方向判断阈值（度）
    var cooldownTime by remember { mutableStateOf("30") } // 冷却时间（秒）
    var delayTime by remember { mutableStateOf("2") } // 延迟检测时间（秒）
    var autoDismissEnabled by remember { mutableStateOf(false) } // 自动消失开关
    var autoDismissSeconds by remember { mutableStateOf("5") } // 自动消失时间（秒）

    // 提醒按钮显示配置状态
    var buttonPosition by remember { mutableStateOf("bottom_left") } // 按钮位置
    var buttonSize by remember { mutableStateOf("56") } // 按钮尺寸
    var buttonMarginX by remember { mutableStateOf("24") } // 水平边距
    var buttonMarginY by remember { mutableStateOf("24") } // 垂直边距

    // 保存函数
    val saveConfig = suspend {
        try {
            // 保存配置
            configAdapter.saveScreenRotationConfig(
                reminderTypeId = reminderType.id,
                sensitivity = sensitivity.toIntOrNull() ?: 45,
                cooldownTime = cooldownTime.toIntOrNull() ?: 30,
                delayTime = delayTime.toIntOrNull() ?: 2,
                autoDismissEnabled = autoDismissEnabled,
                autoDismissSeconds = autoDismissSeconds.toIntOrNull() ?: 5,
                buttonPosition = buttonPosition,
                buttonSize = buttonSize.toIntOrNull() ?: 56,
                buttonMarginX = buttonMarginX.toIntOrNull() ?: 24,
                buttonMarginY = buttonMarginY.toIntOrNull() ?: 24
            )

            // 标记为已配置
            configAdapter.updateConfiguredState(reminderType.id, true)

            // 通知服务重新加载配置
            QuickCommandsService.recheckSmartReminderMonitoring(context)

            // 注意：这里不调用onComplete，避免自动跳转
            // onComplete("screen_rotation_config_saved")
        } catch (e: Exception) {
            // 配置保存失败，可以在这里处理错误
        }
    }

    // 暴露保存函数给外部调用
    LaunchedEffect(onSaveRequested) {
        onSaveRequested?.invoke(saveConfig)
    }

    // 加载初始配置（编辑模式）
    LaunchedEffect(initialConfig) {
        try {
            // 加载屏幕旋转特定配置
            val rotationConfig = configAdapter.loadScreenRotationConfig(reminderType.id)
            sensitivity = rotationConfig.sensitivity.toString()
            cooldownTime = rotationConfig.cooldownTime.toString()
            delayTime = rotationConfig.delayTime.toString()
            autoDismissEnabled = rotationConfig.autoDismissEnabled
            autoDismissSeconds = rotationConfig.autoDismissSeconds.toString()
            buttonPosition = rotationConfig.buttonPosition
            buttonSize = rotationConfig.buttonSize.toString()
            buttonMarginX = rotationConfig.buttonMarginX.toString()
            buttonMarginY = rotationConfig.buttonMarginY.toString()
        } catch (e: Exception) {
            // 加载失败，使用默认值
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {


        // 检测灵敏度
        ConfigSection(
            title = "检测灵敏度",
            description = "调整方向变化的检测阈值，数值越小越敏感（推荐范围：15-75度）"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                OutlinedTextField(
                    value = sensitivity,
                    onValueChange = { newValue ->
                        // 只允许输入数字
                        if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                            sensitivity = newValue
                        }
                    },
                    label = { Text("检测阈值（度）") },
                    placeholder = { Text("45") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    supportingText = {
                        val value = sensitivity.toIntOrNull()
                        when {
                            value == null && sensitivity.isNotEmpty() -> {
                                Text(
                                    text = "请输入有效数字",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null && (value < 15 || value > 75) -> {
                                Text(
                                    text = "建议范围：15-75度",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null -> {
                                Text(
                                    text = when {
                                        value <= 25 -> "高敏感度"
                                        value <= 50 -> "中等敏感度"
                                        else -> "低敏感度"
                                    },
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                )
            }
        }

        // 冷却时间
        ConfigSection(
            title = "提醒间隔",
            description = "两次提醒之间的最小间隔时间，避免频繁打扰（推荐范围：10-300秒）"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                OutlinedTextField(
                    value = cooldownTime,
                    onValueChange = { newValue ->
                        // 只允许输入数字
                        if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                            cooldownTime = newValue
                        }
                    },
                    label = { Text("间隔时间（秒）") },
                    placeholder = { Text("30") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    supportingText = {
                        val value = cooldownTime.toIntOrNull()
                        when {
                            value == null && cooldownTime.isNotEmpty() -> {
                                Text(
                                    text = "请输入有效数字",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null && (value < 10 || value > 300) -> {
                                Text(
                                    text = "建议范围：10-300秒",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null -> {
                                Text(
                                    text = when {
                                        value <= 20 -> "频繁提醒"
                                        value <= 60 -> "适中频率"
                                        else -> "较少打扰"
                                    },
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                )
            }
        }

        // 延迟检测时间
        ConfigSection(
            title = "延迟检测",
            description = "方向变化后的延迟检测时间，给应用适应时间（推荐范围：1-10秒）"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                OutlinedTextField(
                    value = delayTime,
                    onValueChange = { newValue ->
                        // 只允许输入数字
                        if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                            delayTime = newValue
                        }
                    },
                    label = { Text("延迟时间（秒）") },
                    placeholder = { Text("2") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    supportingText = {
                        val value = delayTime.toIntOrNull()
                        when {
                            value == null && delayTime.isNotEmpty() -> {
                                Text(
                                    text = "请输入有效数字",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null && (value < 1 || value > 10) -> {
                                Text(
                                    text = "建议范围：1-10秒",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null -> {
                                Text(
                                    text = when {
                                        value <= 2 -> "快速响应"
                                        value <= 5 -> "平衡模式"
                                        else -> "稳定检测"
                                    },
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                )
            }
        }

        // 自动消失配置
        ConfigSection(
            title = "自动消失",
            description = "设置提醒按钮是否自动消失，避免长时间停留在屏幕上"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                // 自动消失开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "启用自动消失",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "提醒按钮将在指定时间后自动关闭",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = autoDismissEnabled,
                        onCheckedChange = { autoDismissEnabled = it }
                    )
                }

                // 自动消失时间设置
                if (autoDismissEnabled) {
                    OutlinedTextField(
                        value = autoDismissSeconds,
                        onValueChange = { newValue ->
                            // 只允许输入数字
                            if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                                autoDismissSeconds = newValue
                            }
                        },
                        label = { Text("自动消失时间（秒）") },
                        placeholder = { Text("5") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth(),
                        supportingText = {
                            val value = autoDismissSeconds.toIntOrNull()
                            when {
                                value == null && autoDismissSeconds.isNotEmpty() -> {
                                    Text(
                                        text = "请输入有效数字",
                                        color = MaterialTheme.colorScheme.error
                                    )
                                }
                                value != null && (value < 1 || value > 60) -> {
                                    Text(
                                        text = "建议范围：1-60秒",
                                        color = MaterialTheme.colorScheme.error
                                    )
                                }
                                value != null -> {
                                    Text(
                                        text = when {
                                            value <= 3 -> "很快消失"
                                            value <= 10 -> "适中时间"
                                            else -> "较长停留"
                                        },
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                    )
                }
            }
        }

        // 按钮位置配置
        ConfigSection(
            title = "按钮位置",
            description = "设置提醒按钮在屏幕上的显示位置"
        ) {
            val positionOptions = listOf(
                "bottom_left" to "左下角",
                "bottom_right" to "右下角",
                "top_left" to "左上角",
                "top_right" to "右上角"
            )

            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                positionOptions.forEach { (value, label) ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = buttonPosition == value,
                            onClick = { buttonPosition = value }
                        )
                        Text(
                            text = label,
                            modifier = Modifier.padding(start = 8.dp),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }

        // 按钮样式配置
        ConfigSection(
            title = "按钮样式",
            description = "设置提醒按钮的尺寸和边距"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                // 按钮尺寸
                OutlinedTextField(
                    value = buttonSize,
                    onValueChange = { newValue ->
                        if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                            buttonSize = newValue
                        }
                    },
                    label = { Text("按钮尺寸（dp）") },
                    placeholder = { Text("56") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    supportingText = {
                        val value = buttonSize.toIntOrNull()
                        when {
                            value == null && buttonSize.isNotEmpty() -> {
                                Text(
                                    text = "请输入有效数字",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null && (value < 32 || value > 80) -> {
                                Text(
                                    text = "建议范围：32-80dp",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null -> {
                                Text(
                                    text = when {
                                        value <= 40 -> "较小按钮"
                                        value <= 60 -> "标准尺寸"
                                        else -> "较大按钮"
                                    },
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                )

                // 水平边距
                OutlinedTextField(
                    value = buttonMarginX,
                    onValueChange = { newValue ->
                        if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                            buttonMarginX = newValue
                        }
                    },
                    label = { Text("水平边距（dp）") },
                    placeholder = { Text("24") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    supportingText = {
                        val value = buttonMarginX.toIntOrNull()
                        when {
                            value == null && buttonMarginX.isNotEmpty() -> {
                                Text(
                                    text = "请输入有效数字",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null && (value < 0 || value > 100) -> {
                                Text(
                                    text = "建议范围：0-100dp",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null -> {
                                Text(
                                    text = "距离屏幕边缘的水平距离",
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                )

                // 垂直边距
                OutlinedTextField(
                    value = buttonMarginY,
                    onValueChange = { newValue ->
                        if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                            buttonMarginY = newValue
                        }
                    },
                    label = { Text("垂直边距（dp）") },
                    placeholder = { Text("24") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    supportingText = {
                        val value = buttonMarginY.toIntOrNull()
                        when {
                            value == null && buttonMarginY.isNotEmpty() -> {
                                Text(
                                    text = "请输入有效数字",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null && (value < 0 || value > 200) -> {
                                Text(
                                    text = "建议范围：0-200dp",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            value != null -> {
                                Text(
                                    text = "距离屏幕边缘的垂直距离",
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                )
            }
        }
    }
}

/**
 * 配置区域组件
 * 复用屏幕旋转提醒配置界面的ConfigSection组件
 */
@Composable
private fun ConfigSection(
    title: String,
    description: String,
    content: @Composable () -> Unit
) {
    Card(
        shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            content()
        }
    }
}

{"logs": [{"outputFile": "com.weinuo.quickcommands.app-mergeDebugResources-46:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ad2279977a0ab9a9faf3d3cd6fc86c57\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "109,442,443,444,445,446,447,448,449,450,451,454,455,456,457,458,459,460,461,462,463,464,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,837,847", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6458,28062,28150,28236,28317,28401,28470,28535,28618,28724,28810,28930,28984,29053,29114,29183,29272,29367,29441,29538,29631,29729,29878,29969,30057,30153,30251,30315,30383,30470,30564,30631,30703,30775,30876,30985,31061,31130,31178,31244,31308,31382,31439,31496,31568,31618,31672,31743,31814,31884,31953,32011,32087,32158,32232,32318,32368,32438,53574,54289", "endLines": "109,442,443,444,445,446,447,448,449,450,453,454,455,456,457,458,459,460,461,462,463,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,846,849", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "6526,28145,28231,28312,28396,28465,28530,28613,28719,28805,28925,28979,29048,29109,29178,29267,29362,29436,29533,29626,29724,29873,29964,30052,30148,30246,30310,30378,30465,30559,30626,30698,30770,30871,30980,31056,31125,31173,31239,31303,31377,31434,31491,31563,31613,31667,31738,31809,31879,31948,32006,32082,32153,32227,32313,32363,32433,32498,54284,54437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bbaf997ae37e03272ee78df9992f8934\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "103", "startColumns": "4", "startOffsets": "6155", "endColumns": "42", "endOffsets": "6193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bb17b206e660e96f6900b88209c2dff8\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,80,81,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,110,197,198,199,200,201,202,203,716,858,859,863,864,868,876,877,885,891,901,936,957,990", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,543,612,948,1018,1086,1158,1228,1289,1363,1436,1497,1558,1620,1684,1746,1807,1875,1975,2035,2101,2174,2243,2300,2352,2414,2486,2562,2627,2686,2745,2805,2865,2925,2985,3045,3105,3165,3225,3285,3345,3404,3464,3524,3584,3644,3704,3764,3824,3884,3944,4004,4063,4123,4183,4242,4301,4360,4419,4478,4950,4985,5182,5237,5300,5355,5413,5469,5527,5588,5651,5708,5759,5817,5867,5928,5985,6051,6085,6120,6531,12138,12205,12277,12346,12415,12489,12561,45926,54810,54927,55128,55238,55439,56005,56077,56448,56651,56952,58758,59439,60121", "endLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,80,81,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,110,197,198,199,200,201,202,203,716,858,862,863,867,868,876,877,890,900,935,956,989,995", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,607,670,1013,1081,1153,1223,1284,1358,1431,1492,1553,1615,1679,1741,1802,1870,1970,2030,2096,2169,2238,2295,2347,2409,2481,2557,2622,2681,2740,2800,2860,2920,2980,3040,3100,3160,3220,3280,3340,3399,3459,3519,3579,3639,3699,3759,3819,3879,3939,3999,4058,4118,4178,4237,4296,4355,4414,4473,4532,4980,5015,5232,5295,5350,5408,5464,5522,5583,5646,5703,5754,5812,5862,5923,5980,6046,6080,6115,6150,6596,12200,12272,12341,12410,12484,12556,12644,45992,54922,55123,55233,55434,55563,56072,56139,56646,56947,58753,59434,60116,60283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3e109b7321dece0f02b118f7abb21702\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "73,74,75,77,78,108,223,224,282,285,340,385,386,542,552,598,599,640,710,713,714,715,725,732,777,834,850,853", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4537,4611,4669,4790,4841,6405,13790,13855,17621,17808,21366,24158,24210,35098,35715,38525,38575,41154,45624,45798,45844,45886,46454,47014,49800,53462,54442,54553", "endLines": "73,74,75,77,78,108,223,224,282,285,340,385,386,542,552,598,599,640,710,713,714,715,725,732,777,836,852,857", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "4606,4664,4719,4836,4891,6453,13850,13904,17682,17904,21419,24205,24265,35155,35764,38570,38624,41195,45673,45839,45881,45921,46496,47045,49885,53569,54548,54805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f503a90f72cbab575267a43a3d872ea8\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "186,800,801", "startColumns": "4,4,4", "startOffsets": "11481,51363,51419", "endColumns": "45,55,54", "endOffsets": "11522,51414,51469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a24334a9e370adf40f880ddbdf99bd1\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "76", "startColumns": "4", "startOffsets": "4724", "endColumns": "65", "endOffsets": "4785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fbe454da14384bb37833dcce63330c70\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "84,105", "startColumns": "4,4", "startOffsets": "5140,6241", "endColumns": "41,59", "endOffsets": "5177,6296"}}, {"source": "D:\\Users\\Cai_Mouhui\\Documents\\BackgroundManagerMD3\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "30,299,298,32,29,33,31,28,67,96,97,833,836,835,834,832,92,93,153,393,394,198,199,327,328,602,603,604,605,592,593,596,597,88,89,668,669,590,591,664,665,771,774,773,772,770,214,215,1,600,601,666,667,670,671,65,341,662,663,342,594,595,332,331,598,599,216,217,49,48,297,296,383,384,535,242,241,243,682,683,684,685,349,350,560,561,462,463,558,559,562,563,464,465,407,408,11,825,822,302,812,367,368,644,645,646,647,640,641,642,643,636,637,638,639,335,336,534,229,711,712,709,710,652,653,650,651,337,338,570,571,566,567,568,569,470,471,576,577,572,573,574,575,411,412,468,469,64,775,829,377,378,492,493,490,491,422,423,494,495,206,207,240,239,236,237,238,196,197,63,504,505,418,419,385,386,678,679,343,344,674,675,676,677,187,546,547,544,545,188,548,549,554,555,542,543,482,483,550,551,480,481,552,553,167,168,164,165,166,204,205,225,371,372,210,211,146,147,144,145,148,149,224,247,161,139,141,140,632,633,630,631,405,406,628,629,742,745,744,743,741,192,193,195,194,117,115,116,118,45,44,100,101,15,363,364,123,122,121,124,448,449,446,447,444,445,586,587,584,585,409,410,779,780,781,778,373,374,359,360,41,40,184,251,189,190,295,294,191,208,209,107,108,109,104,105,106,355,356,455,456,580,581,457,458,452,453,403,404,454,459,365,366,379,380,688,689,692,693,694,695,696,697,690,691,347,348,698,699,133,134,305,538,539,474,475,476,477,401,402,154,756,758,760,759,757,755,391,392,5,8,6,4,7,749,752,751,750,748,234,235,280,279,500,501,387,388,53,52,498,499,420,421,817,818,811,816,357,358,230,436,437,809,438,439,434,435,430,431,432,433,440,441,813,814,815,399,400,200,201,249,248,60,61,59,250,252,58,389,390,66,810,10,622,623,171,172,173,174,175,413,414,324,323,322,321,320,624,625,735,738,737,736,734,369,370,183,150,119,120,113,114,112,233,731,278,824,821,723,724,719,720,656,657,658,659,721,722,715,716,717,718,339,340,69,220,221,785,788,787,786,784,158,159,160,157,284,287,285,288,286,21,18,19,20,764,767,766,765,763,73,72,75,74,77,76,79,78,246,375,376,794,798,800,791,804,797,792,796,805,793,806,795,803,799,730,729,353,354,68,25,24,316,317,292,291,293,37,36,381,382,202,203,826,823,526,527,508,509,510,511,528,529,532,533,512,513,518,519,524,525,530,531,514,515,520,521,516,517,522,523,179,180,281,267,260,264,257,265,258,275,273,271,272,274,266,259,262,255,263,256,303,304,345,346,312,706,311,705,313,310,702,308,486,309,487,703,704,226,176,62,135,14,13,395,396,618,619,612,613,416,417,614,615,610,611,608,609,616,617,82,83,84,85,136,128,129,130,132,131,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1257,14714,14653,1446,1183,1517,1358,1118,3525,4753,4806,43422,43638,43572,43495,43365,4588,4652,7583,19474,19530,9732,9782,15888,15944,30981,31048,31131,31195,30341,30396,30624,30673,4423,4486,34599,34653,30226,30275,34356,34404,40270,40499,40429,40354,40210,10574,10623,17,30858,30909,34467,34524,34733,34786,3424,16534,34226,34277,16578,30468,30538,16085,16031,30739,30790,10693,10744,2660,2578,14601,14550,18838,18888,27072,11786,11720,11863,35332,35388,35463,35520,16976,17024,28544,28605,23036,23097,28403,28455,28687,28737,23175,23225,20199,20245,380,43130,42957,14799,42491,17850,17903,33363,33413,33483,33533,33116,33169,33239,33292,32880,32932,33002,33050,16172,16226,27020,11107,37116,37171,36990,37041,33762,33816,33631,33683,16297,16348,29142,29213,28840,28905,28991,29054,23480,23549,29595,29658,29305,29365,29446,29509,20424,20476,23335,23397,3371,40566,43273,18449,18502,24606,24655,24482,24535,21029,21077,24727,24794,10208,10263,11681,11613,11403,11460,11541,9628,9672,3314,25249,25308,20785,20838,18959,19015,35162,35222,16640,16687,34896,34948,35020,35080,9111,27619,27692,27475,27539,9161,27786,27852,28229,28292,27321,27391,24126,24186,27937,28002,23982,24044,28085,28149,8217,8267,8054,8107,8155,10084,10138,11007,18092,18137,10443,10490,7279,7336,7165,7214,7407,7451,10968,12039,7983,6931,7055,6988,32706,32760,32576,32625,20074,20128,32444,32494,38686,38910,38836,38759,38625,9408,9452,9570,9515,5739,5636,5687,5788,2413,2325,4896,4955,581,17612,17657,6095,6043,5990,6144,22228,22286,22094,22144,21966,22014,30062,30114,29938,29991,20306,20357,40726,40817,40890,40655,18200,18254,17462,17512,1987,1897,8991,12322,9238,9284,14500,14451,9350,10331,10379,5229,5278,5332,5051,5103,5171,17235,17279,22600,22667,29772,29828,22750,22827,22388,22446,19960,20008,22531,22919,17722,17775,18573,18628,35625,35685,35895,35956,36033,36100,36243,36301,35761,35820,16860,16909,36390,36449,6658,6716,14977,27141,27203,23667,23727,23813,23872,19853,19898,7642,39425,39577,39726,39650,39500,39364,19343,19398,133,289,187,82,237,39061,39275,39205,39131,39003,11291,11345,13796,13747,25070,25137,19090,19143,2918,2842,24921,24986,20910,20962,42770,42826,42441,42722,17338,17391,11163,21541,21593,42329,21659,21716,21425,21475,21179,21235,21307,21359,21790,21850,42540,42604,42663,19745,19790,9848,9898,12182,12115,3146,3201,3092,12258,12373,3039,19216,19270,3472,42381,343,32130,32190,8354,8403,8479,8535,8601,20548,20602,15795,15726,15675,15625,15569,32276,32337,38290,38529,38451,38369,38223,17976,18023,8928,7509,5850,5911,5470,5543,5413,11231,38139,13691,43078,42903,37809,37871,37545,37600,33924,33979,34053,34116,37671,37730,37288,37342,37414,37468,16420,16468,3631,10835,10880,41058,41280,41209,41133,40999,7786,7836,7904,7725,13938,14127,14006,14195,14067,852,657,720,781,39881,40116,40042,39963,39817,3773,3715,3888,3830,4003,3945,4118,4060,11952,18328,18379,41555,41814,41948,41373,42109,41747,41430,41676,42168,41495,42231,41615,42047,41876,38069,38013,17113,17160,3577,1013,943,15447,15497,14342,14290,14395,1694,1609,18704,18761,9964,10014,43182,43011,26540,26594,25431,25485,25560,25615,26663,26717,26905,26953,25697,25740,26042,26090,26417,26467,26792,26839,25797,25842,26152,26202,25911,25965,26276,26335,8759,8825,13861,13259,12796,13038,12601,13112,12668,13606,13487,13369,13429,13547,13185,12731,12872,12462,12958,12532,14856,14909,16752,16797,15291,36872,15228,36789,15356,15164,36567,15048,24293,15105,24375,36650,36710,11047,8680,3256,6803,493,421,19607,19656,31972,32028,31564,31628,20676,20722,31713,31769,31433,31488,31303,31358,31842,31898,4199,4249,4299,4349,6848,6292,6351,6446,6584,6503,6232", "endColumns": "99,60,59,69,72,67,86,63,50,51,65,71,71,64,75,55,62,76,57,54,75,48,64,54,60,65,81,62,78,53,70,47,64,61,75,52,78,47,64,46,61,82,65,68,73,58,47,68,41,49,70,55,73,51,78,46,42,49,77,60,68,84,53,52,49,66,49,66,157,80,50,49,48,69,35,75,64,63,54,73,55,73,46,63,59,80,59,76,50,87,48,69,48,80,44,59,37,50,52,55,47,51,71,48,68,48,66,51,68,51,69,50,68,46,64,52,69,50,54,53,80,49,73,52,75,50,77,49,70,69,90,63,84,61,86,67,88,61,80,58,79,61,84,50,70,60,81,51,64,69,51,69,47,70,51,69,46,64,65,97,53,66,37,66,55,79,70,42,58,55,57,79,51,70,54,73,58,78,45,63,50,70,58,80,48,71,92,62,78,75,64,83,61,77,68,82,58,77,63,81,60,80,62,78,48,62,51,46,60,52,68,38,43,61,45,61,55,69,47,63,42,56,37,74,47,55,85,65,52,88,47,79,52,69,48,80,71,67,72,75,59,42,61,56,53,47,49,50,60,139,86,57,73,47,43,63,47,50,51,62,56,72,48,82,46,78,50,78,51,69,49,65,89,71,84,69,52,72,48,74,313,88,95,49,44,64,48,47,56,46,62,47,52,57,50,66,56,42,57,65,81,54,76,75,90,56,83,46,64,67,87,51,73,53,74,58,74,59,75,65,141,56,87,57,73,47,65,57,84,56,85,46,60,82,58,84,57,78,43,60,55,73,71,66,74,75,59,53,74,52,50,48,49,50,68,64,68,72,56,52,56,63,47,65,80,51,71,94,74,63,82,50,65,54,52,48,46,51,69,43,50,64,50,55,72,48,64,54,70,50,64,58,86,62,57,57,43,61,48,64,74,65,53,53,52,62,66,51,52,71,51,58,35,58,84,47,74,54,64,77,52,70,68,67,49,48,54,59,75,77,72,76,80,65,45,67,61,49,59,77,71,91,55,58,59,54,50,52,60,83,53,69,53,72,61,78,57,77,52,70,52,75,46,64,57,43,65,73,66,69,74,57,48,66,77,59,66,66,59,68,58,68,61,59,69,80,69,72,77,62,55,56,55,56,55,56,55,56,85,49,68,58,60,69,55,57,65,63,69,61,58,73,59,60,70,68,54,45,73,52,79,68,48,47,51,50,54,178,83,55,75,48,68,63,65,52,67,52,73,53,80,52,73,46,65,41,55,46,60,48,71,45,64,43,67,48,72,52,75,57,80,64,80,47,85,72,72,65,71,61,60,58,58,56,57,72,63,84,68,78,67,51,66,43,61,63,82,61,81,64,62,81,55,80,57,75,58,77,37,45,56,43,86,70,47,66,54,70,62,83,44,61,54,71,53,74,53,73,54,72,48,48,48,48,59,57,93,55,72,79,58", "endOffsets": "1352,14770,14708,1511,1251,1580,1440,1177,3571,4800,4867,43489,43705,43632,43566,43416,4646,4724,7636,19524,19601,9776,9842,15938,16000,31042,31125,31189,31269,30390,30462,30667,30733,4480,4557,34647,34727,30269,30335,34398,34461,40348,40560,40493,40423,40264,10617,10687,54,30903,30975,34518,34593,34780,34860,3466,16572,34271,34350,16634,30532,30618,16134,16079,30784,30852,10738,10806,2813,2654,14647,14595,18882,18953,27103,11857,11780,11922,35382,35457,35514,35589,17018,17083,28599,28681,23091,23169,28449,28538,28731,28802,23219,23301,20239,20300,413,43176,43005,14850,42534,17897,17970,33407,33477,33527,33595,33163,33233,33286,33357,32926,32996,33044,33110,16220,16291,27066,11157,37165,37247,37035,37110,33810,33887,33677,33756,16342,16414,29207,29299,28899,28985,29048,29136,23543,23633,29652,29734,29359,29440,29503,29589,20470,20542,23391,23474,3418,40626,43338,18496,18567,24649,24721,24529,24600,21071,21137,24788,24887,10257,10325,11714,11675,11454,11535,11607,9666,9726,3365,25302,25383,20832,20904,19009,19084,35216,35296,16681,16746,34942,35014,35074,35156,9155,27686,27780,27533,27613,9232,27846,27931,28286,28365,27385,27469,24180,24259,27996,28079,24038,24120,28143,28223,8261,8325,8101,8149,8211,10132,10202,11041,18131,18194,10484,10547,7330,7401,7208,7273,7445,7503,11001,12109,8026,6982,7136,7049,32754,32844,32619,32700,20122,20193,32488,32570,38753,38973,38904,38830,38680,9446,9509,9622,9564,5782,5681,5733,5844,2548,2407,4949,5024,624,17651,17716,6138,6089,6037,6202,22280,22354,22138,22222,22008,22088,30108,30188,29985,30056,20351,20418,40811,40884,40970,40720,18248,18322,17506,17582,2296,1981,9082,12367,9278,9344,14544,14494,9402,10373,10437,5272,5326,5385,5097,5165,5223,17273,17332,22661,22744,29822,29900,22821,22913,22440,22525,20002,20068,22594,23002,17769,17844,18622,18698,35679,35755,35950,36027,36094,36237,36295,36384,35814,35889,16903,16970,36443,36529,6710,6797,15019,27197,27281,23721,23807,23866,23946,19892,19954,7693,39494,39644,39788,39720,39571,39419,19392,19468,181,335,231,127,283,39125,39335,39269,39199,39055,11339,11397,13855,13790,25131,25213,19137,19210,3008,2912,24980,25064,20956,21023,42820,42874,42485,42764,17385,17456,11202,21587,21653,42375,21710,21784,21469,21535,21229,21301,21353,21419,21844,21932,42598,42657,42716,19784,19847,9892,9958,12252,12176,3195,3250,3140,12316,12435,3086,19264,19337,3519,42435,374,32184,32270,8397,8473,8529,8595,8674,20596,20668,15859,15789,15720,15669,15619,32331,32408,38363,38597,38523,38445,38284,18017,18086,8985,7554,5905,5984,5537,5630,5464,11285,38194,13741,43124,42951,37865,37950,37594,37665,33973,34047,34110,34190,37724,37803,37336,37408,37462,37539,16462,16528,3684,10874,10941,41127,41342,41274,41203,41052,7830,7898,7977,7780,14000,14189,14061,14259,14121,916,714,775,846,39957,40181,40110,40036,39875,3824,3767,3939,3882,4054,3997,4169,4112,12033,18373,18443,41609,41870,42013,41424,42162,41808,41489,41741,42225,41549,42300,41670,42103,41942,38133,38063,17154,17229,3625,1088,1007,15491,15540,14389,14336,14445,1868,1688,18755,18832,10008,10078,43241,43072,26588,26657,25479,25554,25609,25691,26711,26786,26947,27014,25734,25791,26084,26146,26461,26534,26833,26899,25836,25905,26196,26270,25959,26036,26329,26411,8819,8901,13904,13340,12864,13106,12662,13179,12725,13662,13541,13423,13481,13600,13253,12790,12952,12526,13032,12595,14903,14971,16791,16854,15350,36950,15285,36866,15416,15222,36644,15099,24369,15158,24446,36704,36783,11080,8721,3308,6842,575,487,19650,19718,32022,32094,31622,31707,20716,20779,31763,31836,31482,31558,31352,31427,31892,31966,4243,4293,4343,4393,6903,6345,6440,6497,6652,6578,6286"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,187,188,189,190,191,192,193,194,195,196,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,283,284,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,543,544,545,546,547,548,549,550,551,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,711,712,717,718,719,720,721,722,723,724,726,727,728,729,730,731,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6601,6701,6762,6822,6892,6965,7033,7120,7184,7235,7287,7353,7425,7497,7562,7638,7694,7757,7834,7892,7947,8023,8072,8220,8275,8336,8402,8484,8547,8626,8680,8751,8799,8864,8926,9002,9055,9134,9182,9247,9294,9356,9439,9505,9574,9648,9707,9755,9824,9866,9916,9987,10043,10117,10169,10248,10295,10338,10388,10466,10527,10596,10681,10735,10788,10838,10905,10955,11022,11180,11261,11312,11362,11411,11527,11563,11639,11704,11768,11823,11897,11953,12027,12074,12649,12709,12790,12850,12927,12978,13066,13115,13185,13234,13315,13360,13420,13458,13509,13562,13618,13666,13718,13909,13958,14027,14076,14143,14195,14264,14316,14386,14437,14506,14553,14618,14671,14741,14792,14847,14901,14982,15032,15106,15159,15235,15286,15364,15414,15485,15555,15646,15710,15795,15857,15944,16012,16101,16163,16244,16303,16383,16445,16530,16581,16652,16713,16795,16847,16912,16982,17034,17104,17152,17223,17275,17345,17392,17457,17523,17687,17741,17909,17947,18014,18070,18150,18221,18264,18323,18379,18437,18517,18569,18640,18695,18769,18828,18907,18953,19017,19068,19139,19198,19279,19328,19400,19493,19556,19635,19711,19776,19860,19922,20000,20069,20152,20211,20289,20353,20435,20496,20577,20640,20719,20768,20831,20883,20930,20991,21044,21113,21152,21196,21258,21304,21424,21480,21550,21598,21662,21705,21762,21800,21875,21923,21979,22065,22131,22184,22273,22321,22401,22454,22524,22573,22654,22726,22794,22867,22943,23003,23046,23108,23165,23219,23267,23317,23368,23429,23569,23656,23714,23788,23836,23880,23944,23992,24043,24095,24270,24327,24400,24449,24532,24579,24658,24709,24788,24840,24910,24960,25026,25116,25188,25273,25343,25396,25469,25518,25593,25907,25996,26092,26142,26187,26252,26301,26349,26406,26453,26516,26564,26617,26675,26726,26793,26850,26893,26951,27017,27099,27154,27231,27307,27398,27455,27539,27586,27651,27719,27807,27859,27933,27987,32503,32562,32637,32697,32773,32839,32981,33038,33126,33184,33258,33306,33372,33430,33515,33572,33658,33705,33766,33849,33908,33993,34051,34130,34174,34235,34291,34365,34437,34504,34579,34655,34715,34769,34844,34897,34948,34997,35047,35160,35229,35294,35363,35436,35493,35546,35603,35667,35769,35835,35916,35968,36040,36135,36210,36274,36357,36408,36474,36529,36582,36631,36678,36730,36800,36844,36895,36960,37011,37067,37140,37189,37254,37309,37380,37431,37496,37555,37642,37705,37763,37821,37865,37927,37976,38041,38116,38182,38236,38290,38343,38406,38473,38629,38682,38754,38806,38865,38901,38960,39045,39093,39168,39223,39288,39366,39419,39490,39559,39627,39677,39726,39781,39841,39917,39995,40068,40145,40226,40292,40338,40406,40468,40518,40578,40656,40728,40820,40876,40935,40995,41050,41101,41200,41261,41345,41399,41469,41523,41596,41658,41737,41795,41873,41926,41997,42050,42126,42173,42238,42296,42340,42406,42480,42547,42617,42692,42750,42799,42866,42944,43004,43071,43138,43198,43267,43326,43395,43457,43517,43587,43668,43738,43811,43889,43952,44008,44065,44121,44178,44234,44291,44347,44404,44490,44540,44609,44668,44729,44799,44855,44913,44979,45043,45113,45175,45234,45308,45368,45429,45500,45569,45678,45724,45997,46050,46130,46199,46248,46296,46348,46399,46501,46680,46764,46820,46896,46945,47050,47114,47180,47233,47301,47354,47428,47482,47563,47616,47690,47737,47803,47845,47901,47948,48009,48058,48130,48176,48241,48285,48353,48402,48475,48528,48604,48662,48743,48808,48889,48937,49023,49096,49169,49235,49307,49369,49430,49489,49548,49605,49663,49736,49890,49975,50044,50123,50191,50243,50310,50354,50416,50480,50563,50625,50707,50772,50835,50917,50973,51054,51112,51188,51247,51325,51474,51520,51577,51621,51708,51779,51827,51894,51949,52020,52083,52167,52212,52274,52329,52401,52455,52530,52584,52658,52713,52786,52835,52884,52933,52982,53042,53100,53194,53250,53323,53403", "endColumns": "99,60,59,69,72,67,86,63,50,51,65,71,71,64,75,55,62,76,57,54,75,48,64,54,60,65,81,62,78,53,70,47,64,61,75,52,78,47,64,46,61,82,65,68,73,58,47,68,41,49,70,55,73,51,78,46,42,49,77,60,68,84,53,52,49,66,49,66,157,80,50,49,48,69,35,75,64,63,54,73,55,73,46,63,59,80,59,76,50,87,48,69,48,80,44,59,37,50,52,55,47,51,71,48,68,48,66,51,68,51,69,50,68,46,64,52,69,50,54,53,80,49,73,52,75,50,77,49,70,69,90,63,84,61,86,67,88,61,80,58,79,61,84,50,70,60,81,51,64,69,51,69,47,70,51,69,46,64,65,97,53,66,37,66,55,79,70,42,58,55,57,79,51,70,54,73,58,78,45,63,50,70,58,80,48,71,92,62,78,75,64,83,61,77,68,82,58,77,63,81,60,80,62,78,48,62,51,46,60,52,68,38,43,61,45,61,55,69,47,63,42,56,37,74,47,55,85,65,52,88,47,79,52,69,48,80,71,67,72,75,59,42,61,56,53,47,49,50,60,139,86,57,73,47,43,63,47,50,51,62,56,72,48,82,46,78,50,78,51,69,49,65,89,71,84,69,52,72,48,74,313,88,95,49,44,64,48,47,56,46,62,47,52,57,50,66,56,42,57,65,81,54,76,75,90,56,83,46,64,67,87,51,73,53,74,58,74,59,75,65,141,56,87,57,73,47,65,57,84,56,85,46,60,82,58,84,57,78,43,60,55,73,71,66,74,75,59,53,74,52,50,48,49,50,68,64,68,72,56,52,56,63,47,65,80,51,71,94,74,63,82,50,65,54,52,48,46,51,69,43,50,64,50,55,72,48,64,54,70,50,64,58,86,62,57,57,43,61,48,64,74,65,53,53,52,62,66,51,52,71,51,58,35,58,84,47,74,54,64,77,52,70,68,67,49,48,54,59,75,77,72,76,80,65,45,67,61,49,59,77,71,91,55,58,59,54,50,52,60,83,53,69,53,72,61,78,57,77,52,70,52,75,46,64,57,43,65,73,66,69,74,57,48,66,77,59,66,66,59,68,58,68,61,59,69,80,69,72,77,62,55,56,55,56,55,56,55,56,85,49,68,58,60,69,55,57,65,63,69,61,58,73,59,60,70,68,54,45,73,52,79,68,48,47,51,50,54,178,83,55,75,48,68,63,65,52,67,52,73,53,80,52,73,46,65,41,55,46,60,48,71,45,64,43,67,48,72,52,75,57,80,64,80,47,85,72,72,65,71,61,60,58,58,56,57,72,63,84,68,78,67,51,66,43,61,63,82,61,81,64,62,81,55,80,57,75,58,77,37,45,56,43,86,70,47,66,54,70,62,83,44,61,54,71,53,74,53,73,54,72,48,48,48,48,59,57,93,55,72,79,58", "endOffsets": "6696,6757,6817,6887,6960,7028,7115,7179,7230,7282,7348,7420,7492,7557,7633,7689,7752,7829,7887,7942,8018,8067,8132,8270,8331,8397,8479,8542,8621,8675,8746,8794,8859,8921,8997,9050,9129,9177,9242,9289,9351,9434,9500,9569,9643,9702,9750,9819,9861,9911,9982,10038,10112,10164,10243,10290,10333,10383,10461,10522,10591,10676,10730,10783,10833,10900,10950,11017,11175,11256,11307,11357,11406,11476,11558,11634,11699,11763,11818,11892,11948,12022,12069,12133,12704,12785,12845,12922,12973,13061,13110,13180,13229,13310,13355,13415,13453,13504,13557,13613,13661,13713,13785,13953,14022,14071,14138,14190,14259,14311,14381,14432,14501,14548,14613,14666,14736,14787,14842,14896,14977,15027,15101,15154,15230,15281,15359,15409,15480,15550,15641,15705,15790,15852,15939,16007,16096,16158,16239,16298,16378,16440,16525,16576,16647,16708,16790,16842,16907,16977,17029,17099,17147,17218,17270,17340,17387,17452,17518,17616,17736,17803,17942,18009,18065,18145,18216,18259,18318,18374,18432,18512,18564,18635,18690,18764,18823,18902,18948,19012,19063,19134,19193,19274,19323,19395,19488,19551,19630,19706,19771,19855,19917,19995,20064,20147,20206,20284,20348,20430,20491,20572,20635,20714,20763,20826,20878,20925,20986,21039,21108,21147,21191,21253,21299,21361,21475,21545,21593,21657,21700,21757,21795,21870,21918,21974,22060,22126,22179,22268,22316,22396,22449,22519,22568,22649,22721,22789,22862,22938,22998,23041,23103,23160,23214,23262,23312,23363,23424,23564,23651,23709,23783,23831,23875,23939,23987,24038,24090,24153,24322,24395,24444,24527,24574,24653,24704,24783,24835,24905,24955,25021,25111,25183,25268,25338,25391,25464,25513,25588,25902,25991,26087,26137,26182,26247,26296,26344,26401,26448,26511,26559,26612,26670,26721,26788,26845,26888,26946,27012,27094,27149,27226,27302,27393,27450,27534,27581,27646,27714,27802,27854,27928,27982,28057,32557,32632,32692,32768,32834,32976,33033,33121,33179,33253,33301,33367,33425,33510,33567,33653,33700,33761,33844,33903,33988,34046,34125,34169,34230,34286,34360,34432,34499,34574,34650,34710,34764,34839,34892,34943,34992,35042,35093,35224,35289,35358,35431,35488,35541,35598,35662,35710,35830,35911,35963,36035,36130,36205,36269,36352,36403,36469,36524,36577,36626,36673,36725,36795,36839,36890,36955,37006,37062,37135,37184,37249,37304,37375,37426,37491,37550,37637,37700,37758,37816,37860,37922,37971,38036,38111,38177,38231,38285,38338,38401,38468,38520,38677,38749,38801,38860,38896,38955,39040,39088,39163,39218,39283,39361,39414,39485,39554,39622,39672,39721,39776,39836,39912,39990,40063,40140,40221,40287,40333,40401,40463,40513,40573,40651,40723,40815,40871,40930,40990,41045,41096,41149,41256,41340,41394,41464,41518,41591,41653,41732,41790,41868,41921,41992,42045,42121,42168,42233,42291,42335,42401,42475,42542,42612,42687,42745,42794,42861,42939,42999,43066,43133,43193,43262,43321,43390,43452,43512,43582,43663,43733,43806,43884,43947,44003,44060,44116,44173,44229,44286,44342,44399,44485,44535,44604,44663,44724,44794,44850,44908,44974,45038,45108,45170,45229,45303,45363,45424,45495,45564,45619,45719,45793,46045,46125,46194,46243,46291,46343,46394,46449,46675,46759,46815,46891,46940,47009,47109,47175,47228,47296,47349,47423,47477,47558,47611,47685,47732,47798,47840,47896,47943,48004,48053,48125,48171,48236,48280,48348,48397,48470,48523,48599,48657,48738,48803,48884,48932,49018,49091,49164,49230,49302,49364,49425,49484,49543,49600,49658,49731,49795,49970,50039,50118,50186,50238,50305,50349,50411,50475,50558,50620,50702,50767,50830,50912,50968,51049,51107,51183,51242,51320,51358,51515,51572,51616,51703,51774,51822,51889,51944,52015,52078,52162,52207,52269,52324,52396,52450,52525,52579,52653,52708,52781,52830,52879,52928,52977,53037,53095,53189,53245,53318,53398,53457"}}, {"source": "D:\\Users\\Cai_Mouhui\\Documents\\BackgroundManagerMD3\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,5", "startColumns": "4,4", "startOffsets": "56,148", "endLines": "3,10", "endColumns": "90,12", "endOffsets": "142,490"}, "to": {"startLines": "869,870", "startColumns": "4,4", "startOffsets": "55568,55658", "endLines": "869,875", "endColumns": "89,12", "endOffsets": "55653,56000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\06f299a7430b89abd87e700959100d99\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "6355", "endColumns": "49", "endOffsets": "6400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\576f52a3554e7648cf6b75e678e147f1\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "6198", "endColumns": "42", "endOffsets": "6236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaadb670fa6ca1f2c5a47855923454a7\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "996,1009,1015,1021,1030", "startColumns": "4,4,4,4,4", "startOffsets": "60288,60927,61171,61418,61781", "endLines": "1008,1014,1020,1023,1034", "endColumns": "24,24,24,24,24", "endOffsets": "60922,61166,61413,61546,61958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a60f5ebbf88ef650ff58cf781bd71cd\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "134", "startColumns": "4", "startOffsets": "8137", "endColumns": "82", "endOffsets": "8215"}}, {"source": "D:\\Users\\Cai_Mouhui\\Documents\\BackgroundManagerMD3\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "5,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,675,722,769,816,861,906", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,717,764,811,856,901,943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ff597674a64920f525161048cb2bedc7\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "82,878,1024,1027", "startColumns": "4,4,4,4", "startOffsets": "5020,56144,61551,61666", "endLines": "82,884,1026,1029", "endColumns": "52,24,24,24", "endOffsets": "5068,56443,61661,61776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ea7eaf11704a7bef50ed7e94aaf38b22\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "79,83", "startColumns": "4,4", "startOffsets": "4896,5073", "endColumns": "53,66", "endOffsets": "4945,5135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2be4f9c0dfec74063ff30fa08a680dd9\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "6301", "endColumns": "53", "endOffsets": "6350"}}]}]}
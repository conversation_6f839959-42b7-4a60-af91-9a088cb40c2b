<?xml version="1.0" encoding="utf-8"?>
<!--
快捷指令-手势识别服务配置
用于识别指纹手势操作，支持向上、向下、向左、向右滑动以及轻触、长按手势
采用按需激活设计以最小化资源消耗
-->
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:description="@string/gesture_recognition_accessibility_service_description"
    android:accessibilityEventTypes="typeViewClicked"
    android:accessibilityFlags="flagDefault"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:notificationTimeout="0"
    android:canRetrieveWindowContent="false"
    android:canRequestTouchExplorationMode="false"
    android:canRequestEnhancedWebAccessibility="false"
    android:canRequestFilterKeyEvents="false"
    android:settingsActivity="com.weinuo.quickcommands.MainActivity" />

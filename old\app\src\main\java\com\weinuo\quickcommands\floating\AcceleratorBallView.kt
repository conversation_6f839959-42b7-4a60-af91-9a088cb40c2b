package com.weinuo.quickcommands.floating

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.View
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.WaterBallAdvancedColorSchemes
import com.weinuo.quickcommands.data.SettingsRepository
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import kotlin.math.*

/**
 * 自定义悬浮加速球视图
 * 使用Canvas绘制内存百分比和进度弧线，支持内部动画
 */
class AcceleratorBallView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var memoryPercentage = 0
    private var arcColor = Color.parseColor("#2257F7") // 默认使用低内存占用的颜色
    private var isAnimating = false
    private var animationTime = 0f
    private val maxAnimationTime = 3000f

    // 透明度管理（基于用户交互时间）
    private var currentAlpha = 1.0f
    private var lastInteractionTime = System.currentTimeMillis()
    private val fadeDelayMs = 3000L // 3秒无交互后开始淡化
    private val fadeAlpha = 0.5f // 淡化到50%透明度
    
    // 画笔
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.BLACK
        style = Paint.Style.FILL
    }
    
    private val arcPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = 6f
        strokeCap = Paint.Cap.ROUND
    }
    
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
        textAlign = Paint.Align.CENTER
        typeface = Typeface.DEFAULT_BOLD
    }

    private val percentPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
        textAlign = Paint.Align.CENTER
        typeface = Typeface.DEFAULT
    }

    // 动画相关画笔
    private val cloudPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.argb(80, 255, 255, 255)
    }
    private val waterPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.argb(150, 74, 144, 226)
    }
    private val whalePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.argb(180, 44, 62, 80)
    }
    private val starPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
    }
    private val moonPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.argb(200, 255, 215, 0)
    }
    private val splashPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.argb(120, 135, 206, 235)
    }

    private val animationRunnable = object : Runnable {
        override fun run() {
            if (isAnimating) {
                if (animationTime < maxAnimationTime) {
                    animationTime += 50
                    Log.d("AcceleratorBallView", "动画时间: $animationTime / $maxAnimationTime")
                    invalidate()
                    postDelayed(this, 50) // 20 FPS
                } else {
                    // 动画结束
                    Log.d("AcceleratorBallView", "动画结束")
                    stopAnimation()
                }
            }
        }
    }

    // 透明度检查任务（基于用户交互时间）
    private val alphaCheckRunnable = object : Runnable {
        override fun run() {
            val currentTime = System.currentTimeMillis()
            val timeSinceLastInteraction = currentTime - lastInteractionTime

            val targetAlpha = if (timeSinceLastInteraction >= fadeDelayMs) {
                fadeAlpha // 3秒无交互后淡化到50%
            } else {
                1.0f // 有交互时保持100%不透明
            }

            if (Math.abs(currentAlpha - targetAlpha) > 0.01f) {
                // 平滑过渡透明度
                currentAlpha = if (currentAlpha < targetAlpha) {
                    Math.min(targetAlpha, currentAlpha + 0.05f)
                } else {
                    Math.max(targetAlpha, currentAlpha - 0.05f)
                }
                invalidate()
            }

            // 继续检查
            postDelayed(this, 50) // 每50ms检查一次，实现平滑过渡
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val centerX = width / 2f
        val centerY = height / 2f
        val radius = minOf(width, height) / 2f - 10f

        // 应用透明度
        val alpha = (currentAlpha * 255).toInt()
        backgroundPaint.alpha = alpha
        arcPaint.alpha = alpha
        textPaint.alpha = alpha
        percentPaint.alpha = alpha

        // 绘制黑色圆形背景
        canvas.drawCircle(centerX, centerY, radius, backgroundPaint)

        if (isAnimating) {
            // 动画模式：绘制动画内容
            drawAnimation(canvas, width.toFloat(), height.toFloat())
        } else {
            // 静态模式：绘制进度弧线和文字
            // 绘制进度弧线
            if (memoryPercentage > 0) {
                arcPaint.color = arcColor
                val sweepAngle = (memoryPercentage / 100f) * 360f
                val rect = RectF(
                    centerX - radius + 6f,
                    centerY - radius + 6f,
                    centerX + radius - 6f,
                    centerY + radius - 6f
                )
                canvas.drawArc(rect, -90f, sweepAngle, false, arcPaint)
            }

            // 绘制内存百分比文字
            val numberText = memoryPercentage.toString()
            val percentText = "%"

            val numberSize = radius * 0.7f
            val percentSize = radius * 0.6f

            textPaint.textSize = numberSize
            percentPaint.textSize = percentSize

            // 计算文字位置，确保下边缘对齐
            val numberBounds = Rect()
            val percentBounds = Rect()
            textPaint.getTextBounds(numberText, 0, numberText.length, numberBounds)
            percentPaint.getTextBounds(percentText, 0, percentText.length, percentBounds)

            val baselineY = centerY + numberBounds.height() / 2f
            val numberWidth = textPaint.measureText(numberText)
            val percentWidth = percentPaint.measureText(percentText)
            val totalWidth = numberWidth + percentWidth

            val numberX = centerX - totalWidth / 2f + numberWidth / 2f
            val percentX = centerX + totalWidth / 2f - percentWidth / 2f

            canvas.drawText(numberText, numberX, baselineY, textPaint)
            canvas.drawText(percentText, percentX, baselineY, percentPaint)
        }
    }
    
    /**
     * 更新内存占用百分比
     */
    fun updateMemoryPercentage(percentage: Int) {
        memoryPercentage = percentage.coerceIn(0, 100)
        // 更新弧线颜色
        arcColor = getArcColorForMemoryPercentage(memoryPercentage)

        // 更新用户交互时间
        lastInteractionTime = System.currentTimeMillis()
        currentAlpha = 1.0f

        invalidate()
    }
    
    /**
     * 更新弧线颜色
     */
    fun updateArcColor(color: Int) {
        arcColor = color
        invalidate()
    }

    /**
     * 用户交互事件（点击、拖动等）
     * 重置透明度淡化计时器
     */
    fun onUserInteraction() {
        lastInteractionTime = System.currentTimeMillis()
        currentAlpha = 1.0f
        invalidate()
    }

    /**
     * 开始动画
     */
    fun startAnimation(memoryPercentage: Int) {
        Log.d("AcceleratorBallView", "开始动画，内存占用: $memoryPercentage%")
        this.memoryPercentage = memoryPercentage
        isAnimating = true
        animationTime = 0f

        // 重置透明度和用户交互时间
        currentAlpha = 1.0f
        lastInteractionTime = System.currentTimeMillis()

        removeCallbacks(animationRunnable) // 确保没有重复的回调
        post(animationRunnable)
    }

    /**
     * 停止动画
     */
    fun stopAnimation() {
        isAnimating = false
        removeCallbacks(animationRunnable)
        invalidate()
    }

    /**
     * 绘制动画内容
     */
    private fun drawAnimation(canvas: Canvas, width: Float, height: Float) {
        val centerX = width / 2
        val centerY = height / 2
        val radius = minOf(width, height) / 2 - 10f // 减去边距，确保在圆形内

        // 裁剪为圆形
        val clipPath = Path().apply {
            addCircle(centerX, centerY, radius, Path.Direction.CW)
        }
        canvas.clipPath(clipPath)



        // 绘制星星
        drawStars(canvas, width, height, radius)

        // 绘制云朵
        drawClouds(canvas, width, height, radius)

        // 绘制水体
        drawWater(canvas, width, height, radius)

        // 绘制鲸鱼跃出动画
        drawWhaleJumpAnimation(canvas, width, height, radius)

        // 绘制月亮
        drawMoon(canvas, width, height, radius)
    }



    /**
     * 绘制星星
     */
    private fun drawStars(canvas: Canvas, width: Float, height: Float, radius: Float) {
        val centerX = width / 2
        val centerY = height / 2

        repeat(6) { index ->
            val angle = index * 60f + animationTime * 0.01f
            val distance = radius * 0.3f + (index % 3) * radius * 0.15f
            val starX = centerX + cos(Math.toRadians(angle.toDouble())).toFloat() * distance
            val starY = centerY - radius * 0.3f + sin(Math.toRadians(angle.toDouble())).toFloat() * distance * 0.5f

            val twinkle = sin(animationTime * 0.01f + index) * 0.3f + 0.7f
            starPaint.alpha = (255 * twinkle).toInt()
            canvas.drawCircle(starX, starY, 1.5f, starPaint)
        }
    }

    /**
     * 绘制云朵
     */
    private fun drawClouds(canvas: Canvas, width: Float, height: Float, radius: Float) {
        val centerX = width / 2
        val centerY = height / 2

        repeat(2) { index ->
            val cloudX = centerX - radius + (animationTime * 0.02f + index * radius) % (radius * 2)
            val cloudY = centerY - radius * 0.5f + index * radius * 0.3f
            drawCloudShape(canvas, cloudX, cloudY, radius * 0.15f + index * radius * 0.05f)
        }
    }

    /**
     * 绘制单个云朵
     */
    private fun drawCloudShape(canvas: Canvas, centerX: Float, centerY: Float, size: Float) {
        val baseRadius = size / 4

        // 主体圆形
        canvas.drawCircle(centerX, centerY, baseRadius, cloudPaint)

        // 左侧圆形
        canvas.drawCircle(centerX - baseRadius * 0.6f, centerY, baseRadius * 0.8f, cloudPaint)

        // 右侧圆形
        canvas.drawCircle(centerX + baseRadius * 0.7f, centerY, baseRadius * 0.7f, cloudPaint)

        // 顶部圆形
        canvas.drawCircle(centerX, centerY - baseRadius * 0.5f, baseRadius * 0.6f, cloudPaint)
    }

    /**
     * 绘制水体
     */
    private fun drawWater(canvas: Canvas, width: Float, height: Float, radius: Float) {
        val centerX = width / 2
        val centerY = height / 2

        // 动画过程中水位下降到10%
        val progress = (animationTime / maxAnimationTime).coerceIn(0f, 1f)
        val initialWaterLevel = centerY + radius * (0.2f + memoryPercentage * 0.003f)
        val finalWaterLevel = centerY + radius * 0.8f // 10%水位
        val waterLevel = initialWaterLevel + (finalWaterLevel - initialWaterLevel) * progress

        val waveOffset = animationTime * 0.005f

        val path = Path().apply {
            moveTo(centerX - radius, waterLevel)

            // 绘制波浪
            for (x in (centerX - radius).toInt()..(centerX + radius).toInt() step 5) {
                val waveHeight = sin(((x - centerX + radius) * 0.02f + waveOffset)) * 4f
                lineTo(x.toFloat(), waterLevel + waveHeight)
            }

            lineTo(centerX + radius, centerY + radius)
            lineTo(centerX - radius, centerY + radius)
            close()
        }

        canvas.drawPath(path, waterPaint)
    }

    /**
     * 绘制鲸鱼跃出动画
     */
    private fun drawWhaleJumpAnimation(canvas: Canvas, width: Float, height: Float, radius: Float) {
        val centerX = width / 2
        val centerY = height / 2
        val progress = (animationTime / maxAnimationTime).coerceIn(0f, 1f)

        when {
            // 第一阶段：从中间游到右下角 (0-40%)
            progress <= 0.4f -> {
                val swimProgress = progress / 0.4f
                val whaleX = centerX + (radius * 0.4f) * swimProgress
                val whaleY = centerY + radius * 0.3f + (radius * 0.2f) * swimProgress

                drawWhale(canvas, whaleX, whaleY, 0f)
            }
            // 第二阶段：从左下角跃起越过月亮 (40-80%)
            progress <= 0.8f -> {
                val jumpProgress = (progress - 0.4f) / 0.4f
                val startX = centerX - radius * 0.4f
                val startY = centerY + radius * 0.5f
                val endX = centerX + radius * 0.4f
                val endY = centerY + radius * 0.5f
                val peakY = centerY - radius * 0.2f

                val whaleX = startX + (endX - startX) * jumpProgress
                val whaleY = startY + (peakY - startY) * sin(jumpProgress * PI).toFloat()

                val rotation = jumpProgress * 360f

                drawWhale(canvas, whaleX, whaleY, rotation)

                // 绘制水花效果
                if (jumpProgress > 0.1f && jumpProgress < 0.3f) {
                    drawSplash(canvas, startX, startY)
                }
            }
        }
    }

    /**
     * 绘制鲸鱼
     */
    private fun drawWhale(canvas: Canvas, x: Float, y: Float, rotation: Float) {
        canvas.save()
        canvas.rotate(rotation, x, y)

        // 鲸鱼身体（缩小）
        val whaleRect = RectF(
            x - 15f,
            y - 6f,
            x + 15f,
            y + 6f
        )
        canvas.drawOval(whaleRect, whalePaint)

        // 鲸鱼尾巴（缩小）
        val tailPath = Path().apply {
            moveTo(x - 15f, y)
            lineTo(x - 22f, y - 4f)
            lineTo(x - 18f, y)
            lineTo(x - 22f, y + 4f)
            close()
        }
        canvas.drawPath(tailPath, whalePaint)

        // 鲸鱼眼睛（缩小）
        canvas.drawCircle(x + 6f, y - 2f, 1.5f, starPaint)

        canvas.restore()
    }

    /**
     * 绘制水花效果
     */
    private fun drawSplash(canvas: Canvas, x: Float, y: Float) {
        repeat(6) { i ->
            val angle = i * 60f
            val splashX = x + cos(Math.toRadians(angle.toDouble())).toFloat() * 10f
            val splashY = y + sin(Math.toRadians(angle.toDouble())).toFloat() * 8f
            canvas.drawCircle(splashX, splashY, 2f, splashPaint)
        }
    }

    /**
     * 绘制月亮
     */
    private fun drawMoon(canvas: Canvas, width: Float, height: Float, radius: Float) {
        val centerX = width / 2
        val centerY = height / 2
        val progress = (animationTime / maxAnimationTime).coerceIn(0f, 1f)

        // 月亮在动画后半段出现
        if (progress > 0.5f) {
            val moonProgress = (progress - 0.5f) * 2f
            val moonX = centerX + radius + radius * 0.3f - moonProgress * (radius * 0.8f)
            val moonY = centerY - radius * 0.6f

            // 绘制月亮（缩小）
            canvas.drawCircle(moonX, moonY, radius * 0.12f, moonPaint)

            // 绘制月亮辉光（缩小）
            val glowPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                color = Color.argb((20 * moonProgress).toInt(), 255, 215, 0)
            }
            canvas.drawCircle(moonX, moonY, radius * 0.18f, glowPaint)
        }
    }

    /**
     * 根据内存占用百分比获取弧线颜色
     * 实现与水球颜色的反向映射，支持主题感知和高级材质
     */
    private fun getArcColorForMemoryPercentage(memoryPercentage: Int): Int {
        try {
            // 获取主题管理器和设置
            val themeManager = ThemeManager.getInstance(context)
            val currentTheme = themeManager.currentTheme.value
            val settingsRepository = SettingsRepository(context)

            // 获取全局设置（同步方式，因为在View中）
            val globalSettings = runBlocking { settingsRepository.globalSettings.first() }

            // 判断是否启用高级材质效果（仅在天空蓝主题下有效）
            val isAdvancedMaterialEnabled = globalSettings.waterBallAdvancedMaterialEnabled &&
                                          currentTheme.id == "sky_blue"

            return when {
                // 内存占用低（0-59%）-> 对应水球高分数颜色（优秀）
                memoryPercentage <= 59 -> {
                    if (isAdvancedMaterialEnabled) {
                        // 使用高级材质的优秀等级颜色
                        try {
                            val colorSet = WaterBallAdvancedColorSchemes.getColorSetForScore(100)
                            // 将Compose Color转换为Android Color
                            val composeColor = colorSet.gradientEnd
                            Color.argb(
                                (composeColor.alpha * 255).toInt(),
                                (composeColor.red * 255).toInt(),
                                (composeColor.green * 255).toInt(),
                                (composeColor.blue * 255).toInt()
                            )
                        } catch (e: Exception) {
                            Log.e("AcceleratorBallView", "获取高级材质颜色失败", e)
                            Color.parseColor("#2257F7")
                        }
                    } else {
                        // 根据主题使用品牌色
                        when (currentTheme.id) {
                            "sky_blue" -> Color.parseColor("#2257F7")
                            "ocean_blue" -> Color.parseColor("#1976D2")
                            else -> Color.parseColor("#1976D2")
                        }
                    }
                }
                // 内存占用中等（60-79%）-> 对应水球中等分数颜色（良好）
                memoryPercentage <= 79 -> {
                    if (isAdvancedMaterialEnabled) {
                        // 使用高级材质的良好等级颜色
                        try {
                            val colorSet = WaterBallAdvancedColorSchemes.getColorSetForScore(70)
                            // 将Compose Color转换为Android Color
                            val composeColor = colorSet.gradientEnd
                            Color.argb(
                                (composeColor.alpha * 255).toInt(),
                                (composeColor.red * 255).toInt(),
                                (composeColor.green * 255).toInt(),
                                (composeColor.blue * 255).toInt()
                            )
                        } catch (e: Exception) {
                            Log.e("AcceleratorBallView", "获取高级材质颜色失败", e)
                            Color.parseColor("#FFAA19")
                        }
                    } else {
                        // 根据主题选择橙色
                        when (currentTheme.id) {
                            "sky_blue" -> Color.parseColor("#FFAA19")
                            "ocean_blue" -> Color.parseColor("#FF9800")
                            else -> Color.parseColor("#FF9800")
                        }
                    }
                }
                // 内存占用高（80-100%）-> 对应水球低分数颜色（需要优化）
                else -> {
                    if (isAdvancedMaterialEnabled) {
                        // 使用高级材质的需要优化等级颜色
                        try {
                            val colorSet = WaterBallAdvancedColorSchemes.getColorSetForScore(30)
                            // 将Compose Color转换为Android Color
                            val composeColor = colorSet.gradientEnd
                            Color.argb(
                                (composeColor.alpha * 255).toInt(),
                                (composeColor.red * 255).toInt(),
                                (composeColor.green * 255).toInt(),
                                (composeColor.blue * 255).toInt()
                            )
                        } catch (e: Exception) {
                            Log.e("AcceleratorBallView", "获取高级材质颜色失败", e)
                            Color.parseColor("#EA623D")
                        }
                    } else {
                        // 使用错误色
                        Color.parseColor("#EA623D")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("AcceleratorBallView", "获取弧线颜色失败", e)
            // 降级到默认颜色
            return when {
                memoryPercentage <= 59 -> Color.parseColor("#2257F7")
                memoryPercentage <= 79 -> Color.parseColor("#FFAA19")
                else -> Color.parseColor("#EA623D")
            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        // 启动透明度检查
        post(alphaCheckRunnable)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 停止所有回调
        removeCallbacks(animationRunnable)
        removeCallbacks(alphaCheckRunnable)
    }
}

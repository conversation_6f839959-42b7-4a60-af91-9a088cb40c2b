{"logs": [{"outputFile": "com.weinuo.quickcommands.app-mergeDebugResources-46:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f503a90f72cbab575267a43a3d872ea8\\transformed\\foundation-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,235", "endColumns": "79,99,101", "endOffsets": "130,230,332"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9046,9146", "endColumns": "79,99,101", "endOffsets": "180,9141,9243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3e109b7321dece0f02b118f7abb21702\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,285,367,465,568,657,736,832,924,1011,1098,1188,1265,1350,1426,1506,1582,1660,1730", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,84,75,79,75,77,69,122", "endOffsets": "280,362,460,563,652,731,827,919,1006,1093,1183,1260,1345,1421,1501,1577,1655,1725,1848"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "917,1016,1098,1196,1299,1388,1467,7924,8016,8103,8190,8280,8357,8442,8518,8598,8775,8853,8923", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,84,75,79,75,77,69,122", "endOffsets": "1011,1093,1191,1294,1383,1462,1558,8011,8098,8185,8275,8352,8437,8513,8593,8669,8848,8918,9041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ad2279977a0ab9a9faf3d3cd6fc86c57\\transformed\\material3-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,299,422,542,642,740,855,998,1116,1268,1353,1455,1552,1654,1772,1895,2002,2138,2271,2410,2592,2723,2843,2965,3092,3190,3286,3407,3540,3641,3746,3861,3996,4137,4248,4353,4430,4526,4621,4742,4829,4918,5029,5109,5193,5294,5400,5500,5599,5687,5802,5903,6007,6130,6210,6317", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,120,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "172,294,417,537,637,735,850,993,1111,1263,1348,1450,1547,1649,1767,1890,1997,2133,2266,2405,2587,2718,2838,2960,3087,3185,3281,3402,3535,3636,3741,3856,3991,4132,4243,4348,4425,4521,4616,4737,4824,4913,5024,5104,5188,5289,5395,5495,5594,5682,5797,5898,6002,6125,6205,6312,6411"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1563,1685,1807,1930,2050,2150,2248,2363,2506,2624,2776,2861,2963,3060,3162,3280,3403,3510,3646,3779,3918,4100,4231,4351,4473,4600,4698,4794,4915,5048,5149,5254,5369,5504,5645,5756,5861,5938,6034,6129,6250,6337,6426,6537,6617,6701,6802,6908,7008,7107,7195,7310,7411,7515,7638,7718,7825", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,120,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "1680,1802,1925,2045,2145,2243,2358,2501,2619,2771,2856,2958,3055,3157,3275,3398,3505,3641,3774,3913,4095,4226,4346,4468,4595,4693,4789,4910,5043,5144,5249,5364,5499,5640,5751,5856,5933,6029,6124,6245,6332,6421,6532,6612,6696,6797,6903,7003,7102,7190,7305,7406,7510,7633,7713,7820,7919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bb17b206e660e96f6900b88209c2dff8\\transformed\\core-1.16.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "185,284,386,486,584,691,797,8674", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "279,381,481,579,686,792,912,8770"}}]}]}
package com.weinuo.quickcommands.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.configuration.ConfigurationItem

/**
 * 分类展示列表组件
 *
 * 使用LazyColumn以长条形卡片布局展示配置项，提供统一的卡片式设计。
 * 支持点击操作，集成Material Design 3主题。
 *
 * @param items 配置项列表
 * @param onItemSelected 配置项选中回调
 * @param modifier 修饰符
 */
@Composable
fun CategoryDisplayGrid(
    items: List<ConfigurationItem>,
    onItemSelected: (ConfigurationItem) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = modifier
    ) {
        items(items) { item ->
            CategoryCard(
                item = item,
                onClick = { onItemSelected(item) }
            )
        }
    }
}

/**
 * 分类卡片组件
 *
 * 单个配置项的长条形卡片展示，包含图标、标题和描述。
 * 使用Material Design 3颜色系统，采用与电话任务配置界面相同的样式。
 *
 * @param item 配置项数据
 * @param onClick 点击回调
 */
@Composable
private fun CategoryCard(
    item: ConfigurationItem,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        ),
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧圆形彩色图标背景
            val iconBackgroundColor = remember(item.id) {
                // 根据item.id生成不同的颜色
                val colors = listOf(
                    Color(0xFF2196F3), // 蓝色
                    Color(0xFF4CAF50), // 绿色
                    Color(0xFFFF9800), // 橙色
                    Color(0xFF9C27B0), // 紫色
                    Color(0xFFF44336), // 红色
                    Color(0xFF00BCD4), // 青色
                    Color(0xFFFFEB3B), // 黄色
                    Color(0xFF795548), // 棕色
                    Color(0xFF607D8B), // 蓝灰色
                    Color(0xFFE91E63)  // 粉色
                )
                colors[item.id.hashCode().mod(colors.size)]
            }

            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(iconBackgroundColor),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = item.icon,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = Color.White
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 右侧文本内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 标题
                Text(
                    text = item.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.height(4.dp))

                // 描述
                Text(
                    text = item.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

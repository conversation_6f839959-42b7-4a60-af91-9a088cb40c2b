<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WizardSettings">
    <option name="children">
      <map>
        <entry key="vectorWizard">
          <value>
            <PersistentState>
              <option name="children">
                <map>
                  <entry key="vectorAssetStep">
                    <value>
                      <PersistentState>
                        <option name="values">
                          <map>
                            <entry key="assetSourceType" value="FILE" />
                            <entry key="outputName" value="ic_radio_button_checked_sky_blue" />
                            <entry key="sourceFile" value="D:\Users\Cai_Mouhui\Downloads\checkmark_circle_fill.svg" />
                          </map>
                        </option>
                      </PersistentState>
                    </value>
                  </entry>
                </map>
              </option>
            </PersistentState>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>
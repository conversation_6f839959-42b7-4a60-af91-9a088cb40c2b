package com.weinuo.quickcommands.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.currentBackStackEntryAsState
import com.weinuo.quickcommands.ui.components.themed.ThemedBottomNavigation
import com.weinuo.quickcommands.ui.theme.config.NavigationTab
import com.weinuo.quickcommands.ui.effects.HazeManager
import com.weinuo.quickcommands.utils.ExperimentalFeatureDetector
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import dev.chrisbanes.haze.HazeState

/**
 * 主题感知的底部导航栏组件
 *
 * 这是对现有BottomNavBar的主题感知版本，
 * 将现有的导航逻辑适配到新的主题系统中，
 * 支持iOS风格的模糊效果。
 */
@Composable
fun ThemedBottomNavBar(
    navController: NavController,
    experimentalFeatureDetector: ExperimentalFeatureDetector? = null,
    hazeState: HazeState? = null,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    // 获取或创建HazeState
    val actualHazeState = hazeState ?: HazeManager.getInstance(context).globalHazeState

    // 获取设置仓库和导航项显示状态（仅在天空蓝主题下使用）
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 根据主题和设置过滤导航项
    val visibleBottomNavItems = remember(globalSettings.navigationItemsVisibility, themeManager.getCurrentThemeId()) {
        if (themeManager.getCurrentThemeId() == "sky_blue") {
            // 天空蓝主题：根据设置过滤导航项
            bottomNavItems.filter { screen ->
                val routeKey = when (screen.route) {
                    "phone_checkup" -> "phone_checkup"
                    "quick_commands" -> "quick_commands"
                    "command_templates" -> "command_templates"
                    "smart_reminders" -> "smart_reminders"
                    "global_settings" -> "global_settings"
                    else -> null
                }
                routeKey?.let { globalSettings.navigationItemsVisibility[it] } ?: true
            }
        } else {
            // 其他主题：显示所有导航项
            bottomNavItems
        }
    }

    // 将Screen对象转换为NavigationTab
    val tabs = visibleBottomNavItems.map { screen ->
        NavigationTab(
            label = stringResource(id = screen.titleResId),
            icon = screen.unselectedIcon,
            selectedIcon = screen.selectedIcon
        )
    }

    // 计算当前选中的索引（基于可见的导航项）
    val selectedIndex = visibleBottomNavItems.indexOfFirst { screen ->
        currentDestination?.route == screen.route
    }.takeIf { it >= 0 } ?: 0

    // 使用主题感知的底部导航
    ThemedBottomNavigation(
        tabs = tabs,
        selectedIndex = selectedIndex,
        onTabSelected = { index ->
            val selectedScreen = visibleBottomNavItems[index]

            // 如果是全局设置导航项且有实验性功能检测器，处理点击检测
            if (selectedScreen == Screen.GlobalSettings && experimentalFeatureDetector != null) {
                experimentalFeatureDetector.handleClick(ExperimentalFeatureDetector.ClickTarget.NAVIGATION_ITEM)
            }

            navController.navigate(selectedScreen.route) {
                // 避免创建多个返回栈
                popUpTo(navController.graph.findStartDestination().id) {
                    saveState = true
                }
                // 避免多次点击创建多个实例
                launchSingleTop = true
                // 恢复状态
                restoreState = true
            }
        },
        hazeState = actualHazeState,
        modifier = modifier
    )
}

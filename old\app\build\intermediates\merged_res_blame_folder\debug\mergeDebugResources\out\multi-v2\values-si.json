{"logs": [{"outputFile": "com.weinuo.quickcommands.app-mergeDebugResources-46:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ad2279977a0ab9a9faf3d3cd6fc86c57\\transformed\\material3-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,399,516,614,711,825,954,1074,1213,1297,1403,1494,1591,1705,1833,1944,2072,2198,2330,2503,2627,2744,2864,2985,3077,3172,3291,3412,3513,3616,3720,3851,3987,4094,4191,4267,4363,4461,4566,4652,4741,4835,4918,5001,5100,5200,5292,5393,5481,5592,5694,5806,5927,6009,6117", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "166,283,394,511,609,706,820,949,1069,1208,1292,1398,1489,1586,1700,1828,1939,2067,2193,2325,2498,2622,2739,2859,2980,3072,3167,3286,3407,3508,3611,3715,3846,3982,4089,4186,4262,4358,4456,4561,4647,4736,4830,4913,4996,5095,5195,5287,5388,5476,5587,5689,5801,5922,6004,6112,6211"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1544,1660,1777,1888,2005,2103,2200,2314,2443,2563,2702,2786,2892,2983,3080,3194,3322,3433,3561,3687,3819,3992,4116,4233,4353,4474,4566,4661,4780,4901,5002,5105,5209,5340,5476,5583,5680,5756,5852,5950,6055,6141,6230,6324,6407,6490,6589,6689,6781,6882,6970,7081,7183,7295,7416,7498,7606", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "1655,1772,1883,2000,2098,2195,2309,2438,2558,2697,2781,2887,2978,3075,3189,3317,3428,3556,3682,3814,3987,4111,4228,4348,4469,4561,4656,4775,4896,4997,5100,5204,5335,5471,5578,5675,5751,5847,5945,6050,6136,6225,6319,6402,6485,6584,6684,6776,6877,6965,7076,7178,7290,7411,7493,7601,7700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3e109b7321dece0f02b118f7abb21702\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,274,357,456,555,637,722,813,899,979,1058,1140,1213,1297,1372,1456,1537,1618,1685", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,83,74,83,80,80,66,117", "endOffsets": "269,352,451,550,632,717,808,894,974,1053,1135,1208,1292,1367,1451,1532,1613,1680,1798"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "916,1005,1088,1187,1286,1368,1453,7705,7791,7871,7950,8032,8105,8189,8264,8348,8530,8611,8678", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,83,74,83,80,80,66,117", "endOffsets": "1000,1083,1182,1281,1363,1448,1539,7786,7866,7945,8027,8100,8184,8259,8343,8424,8606,8673,8791"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bb17b206e660e96f6900b88209c2dff8\\transformed\\core-1.16.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "184,286,389,494,599,698,802,8429", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "281,384,489,594,693,797,911,8525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f503a90f72cbab575267a43a3d872ea8\\transformed\\foundation-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,224", "endColumns": "78,89,92", "endOffsets": "129,219,312"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8796,8886", "endColumns": "78,89,92", "endOffsets": "179,8881,8974"}}]}]}
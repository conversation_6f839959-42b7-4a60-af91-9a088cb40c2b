<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.weinuo.quickcommands"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="35" />

    <!-- 权限声明 -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- 音频相关权限 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />

    <!-- 网络相关权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- 通知权限 (Android 13+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 后台进程管理权限 -->
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />

    <!-- 前台服务权限 (Android 9+) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- 前台服务类型权限 (Android 14+) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <!-- 精确闹钟权限 (Android 12+) - 用于时间条件监控 -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />

    <!-- 通信状态条件相关权限 -->
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />

    <!-- 账号相关权限 -->
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />

    <!-- 传感器状态条件相关权限 -->
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />

    <!-- 位置相关权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- 蓝牙相关权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <!-- 设备管理器相关权限 -->
    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />

    <!-- 媒体相关权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- 相机相关权限 -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- 传感器相关的uses-feature声明 -->
    <uses-feature
        android:name="android.hardware.sensor.accelerometer"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.sensor.light"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.sensor.proximity"
        android:required="false" />

    <permission
        android:name="com.weinuo.quickcommands.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.weinuo.quickcommands.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
    <uses-permission android:name="moe.shizuku.manager.permission.API_V23" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.QuickCommands" >
        <activity
            android:name="com.weinuo.quickcommands.MainActivity"
            android:allowEmbedded="true"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:resizeableActivity="true"
            android:theme="@style/Theme.QuickCommands" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- 声明静态快捷方式 -->
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />
        </activity>

        <!-- 气泡通知专用Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.BubbleActivity"
            android:allowEmbedded="true"
            android:documentLaunchMode="always"
            android:exported="false"
            android:launchMode="singleTask"
            android:resizeableActivity="true"
            android:taskAffinity=""
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 手势录制Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.recording.GestureRecordingActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.QuickCommands.NoActionBar" >
        </activity>

        <!-- 手势录制编辑Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 快捷指令执行器活动 -->
        <activity
            android:name="com.weinuo.quickcommands.shortcut.QuickCommandExecutorActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands.NoActionBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- 静态快捷方式处理活动 -->
        <activity
            android:name="com.weinuo.quickcommands.shortcut.StaticShortcutHandlerActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands.NoActionBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- 桌面小组件点击处理活动 -->
        <activity
            android:name="com.weinuo.quickcommands.widget.WidgetClickHandlerActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands.NoActionBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- 快捷指令表单Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.QuickCommandFormActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 图标选择Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.IconSelectionActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 应用选择Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.AppSelectionActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 统一配置Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.UnifiedConfigurationActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 详细配置Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.DetailedConfigurationActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 联系人选择Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.ContactSelectionActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 铃声选择Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.RingtoneSelectionActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 分享目标选择Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.ShareTargetSelectionActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 高级内存配置Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.AdvancedMemoryConfigActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 内存学习数据Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.MemoryLearningDataActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 高级清理策略配置Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.AdvancedCleanupStrategyActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 添加清理规则Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.AddCleanupRuleActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 智慧提醒详细配置Activity -->
        <activity
            android:name="com.weinuo.quickcommands.ui.activities.SmartReminderDetailConfigActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.QuickCommands" >
        </activity>

        <!-- 桌面小组件1 -->
        <receiver
            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget1"
            android:exported="true"
            android:label="@string/widget_1_label" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/one_click_command_widget_1_info" />
        </receiver>

        <!-- 桌面小组件2 -->
        <receiver
            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget2"
            android:exported="true"
            android:label="@string/widget_2_label" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/one_click_command_widget_2_info" />
        </receiver>

        <!-- 桌面小组件3 -->
        <receiver
            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget3"
            android:exported="true"
            android:label="@string/widget_3_label" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/one_click_command_widget_3_info" />
        </receiver>

        <!-- 桌面小组件4 -->
        <receiver
            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget4"
            android:exported="true"
            android:label="@string/widget_4_label" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/one_click_command_widget_4_info" />
        </receiver>

        <!-- 设备管理器接收器 -->
        <receiver
            android:name="com.weinuo.quickcommands.permission.DeviceAdminReceiver"
            android:exported="true"
            android:permission="android.permission.BIND_DEVICE_ADMIN" >
            <meta-data
                android:name="android.app.device_admin"
                android:resource="@xml/device_admin" />

            <intent-filter>
                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
            </intent-filter>
        </receiver>

        <!-- Shizuku UserService -->
        <provider
            android:name="rikka.shizuku.ShizukuProvider"
            android:authorities="com.weinuo.quickcommands.shizuku"
            android:enabled="true"
            android:exported="true"
            android:multiprocess="false"
            android:permission="android.permission.INTERACT_ACROSS_USERS_FULL" />

        <!-- 快捷指令服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.QuickCommandsService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- 闹钟悬浮窗服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.AlarmOverlayService"
            android:enabled="true"
            android:exported="false" />

        <!-- 触摸屏蔽悬浮窗服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.TouchBlockOverlayService"
            android:enabled="true"
            android:exported="false" />

        <!-- 剪贴板刷新叠加层服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.ClipboardRefreshOverlayService"
            android:enabled="true"
            android:exported="false" />

        <!-- 悬浮按钮服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.FloatingButtonService"
            android:enabled="true"
            android:exported="false" />

        <!-- 智慧提醒悬浮窗服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.SmartReminderOverlayService"
            android:enabled="true"
            android:exported="false" />

        <!-- 悬浮录制服务 -->
        <service
            android:name="com.weinuo.quickcommands.floating.FloatingRecordingService"
            android:enabled="true"
            android:exported="false" />

        <!-- 悬浮加速球服务 -->
        <service
            android:name="com.weinuo.quickcommands.floating.FloatingAcceleratorService"
            android:enabled="true"
            android:exported="false" />

        <!-- 高级悬浮录制服务 -->
        <service
            android:name="com.weinuo.quickcommands.floating.AdvancedFloatingRecordingService"
            android:enabled="true"
            android:exported="false" />

        <!-- 快捷指令-系统优先级提升无障碍服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.SystemPriorityEnhancementService"
            android:description="@string/accessibility_service_system_description"
            android:exported="true"
            android:label="@string/accessibility_service_name"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- 快捷指令-系统操作无障碍服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.SystemOperationAccessibilityService"
            android:description="@string/system_operation_accessibility_service_description"
            android:exported="true"
            android:label="@string/system_operation_accessibility_service_name"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/system_operation_accessibility_service_config" />
        </service>

        <!-- 快捷指令-界面交互服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService"
            android:description="@string/interface_interaction_accessibility_service_description"
            android:exported="true"
            android:label="@string/interface_interaction_accessibility_service_name"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/interface_interaction_accessibility_service_config" />
        </service>

        <!-- 快捷指令-手势识别服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.GestureRecognitionAccessibilityService"
            android:description="@string/gesture_recognition_accessibility_service_description"
            android:exported="true"
            android:label="@string/gesture_recognition_accessibility_service_name"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/gesture_recognition_accessibility_service_config" />
        </service>

        <!-- 快捷指令-自动点击器服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.AutoClickerAccessibilityService"
            android:description="@string/auto_clicker_accessibility_service_description"
            android:exported="true"
            android:label="@string/auto_clicker_accessibility_service_name"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/auto_clicker_accessibility_service_config" />
        </service>

        <!-- 通知监听服务 -->
        <service
            android:name="com.weinuo.quickcommands.service.QuickCommandsNotificationService"
            android:description="@string/notification_listener_service_description"
            android:exported="true"
            android:label="@string/notification_listener_service_name"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <!-- FileProvider for camera file sharing -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.weinuo.quickcommands.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.weinuo.quickcommands.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="moe.shizuku.client.V3_SUPPORT"
            android:value="true" />
    </application>

</manifest>
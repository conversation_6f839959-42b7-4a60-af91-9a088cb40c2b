package com.weinuo.quickcommands.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Article
import androidx.compose.material.icons.automirrored.outlined.Article
import androidx.compose.material.icons.filled.Apps
import androidx.compose.material.icons.outlined.Apps
import androidx.compose.material.icons.outlined.Settings
import androidx.compose.material.icons.filled.AutoAwesome
import androidx.compose.material.icons.filled.Lightbulb
import androidx.compose.material.icons.filled.MonitorHeart
import androidx.compose.material.icons.filled.Security
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Timer
import androidx.compose.material.icons.outlined.AutoAwesome
import androidx.compose.material.icons.outlined.Lightbulb
import androidx.compose.material.icons.outlined.MonitorHeart
import androidx.compose.material.icons.outlined.Security
import androidx.compose.material.icons.outlined.Share
import androidx.compose.material.icons.outlined.Timer
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Text
import com.weinuo.quickcommands.ui.theme.UnselectedIconGray
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.currentBackStackEntryAsState
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.utils.ExperimentalFeatureDetector

/**
 * 定义应用的导航目标
 */
sealed class Screen(
    val route: String,
    val titleResId: Int,
    val selectedIcon: ImageVector,
    val unselectedIcon: ImageVector
) {


    object QuickCommands : Screen(
        route = "quick_commands",
        titleResId = R.string.nav_quick_commands,
        selectedIcon = Icons.Filled.AutoAwesome,
        unselectedIcon = Icons.Outlined.AutoAwesome
    )

    object GlobalSettings : Screen(
        route = "global_settings",
        titleResId = R.string.nav_global_settings,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    )

    // 手机体检界面
    object PhoneCheckup : Screen(
        route = "phone_checkup",
        titleResId = R.string.nav_phone_checkup,
        selectedIcon = Icons.Filled.MonitorHeart,
        unselectedIcon = Icons.Outlined.MonitorHeart
    )

    // 智慧提醒界面
    object SmartReminders : Screen(
        route = "smart_reminders",
        titleResId = R.string.nav_smart_reminders,
        selectedIcon = Icons.Filled.Lightbulb,
        unselectedIcon = Icons.Outlined.Lightbulb
    )

    // 非底部导航栏目标
    // 指令模板界面
    object CommandTemplates : Screen(
        route = "command_templates",
        titleResId = R.string.nav_command_templates,
        selectedIcon = Icons.AutoMirrored.Filled.Article,
        unselectedIcon = Icons.AutoMirrored.Outlined.Article
    )
    // 统一快捷指令表单界面（支持创建和编辑）
    object QuickCommandForm : Screen(
        route = "quick_command_form?commandId={commandId}",
        titleResId = R.string.quick_command_form,
        selectedIcon = Icons.Filled.AutoAwesome,  // 这些图标不会显示，因为不在底部导航栏
        unselectedIcon = Icons.Outlined.AutoAwesome
    ) {
        // 创建新快捷指令的路由（不传commandId）
        fun createNewRoute(): String {
            return "quick_command_form"
        }

        // 编辑现有快捷指令的路由（传入commandId）
        fun createEditRoute(commandId: String): String {
            return "quick_command_form?commandId=$commandId"
        }
    }

    // 统一配置选择界面（支持条件、中止条件、任务三种模式，支持编辑模式）
    object UnifiedConfiguration : Screen(
        route = "unified_configuration/{configurationMode}?editData={editData}&editIndex={editIndex}",
        titleResId = R.string.unified_configuration,
        selectedIcon = Icons.Filled.Settings,  // 这些图标不会显示，因为不在底部导航栏
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 添加触发条件的路由
        fun createTriggerConditionRoute(): String {
            return "unified_configuration/TRIGGER_CONDITION"
        }

        // 添加中止条件的路由
        fun createAbortConditionRoute(): String {
            return "unified_configuration/ABORT_CONDITION"
        }

        // 添加任务的路由
        fun createTaskRoute(): String {
            return "unified_configuration/TASK"
        }

        // 编辑触发条件的路由
        fun createEditTriggerConditionRoute(conditionData: String, index: Int): String {
            val encodedData = java.net.URLEncoder.encode(conditionData, "UTF-8")
            return "unified_configuration/TRIGGER_CONDITION?editData=$encodedData&editIndex=$index"
        }

        // 编辑中止条件的路由
        fun createEditAbortConditionRoute(conditionData: String, index: Int): String {
            val encodedData = java.net.URLEncoder.encode(conditionData, "UTF-8")
            return "unified_configuration/ABORT_CONDITION?editData=$encodedData&editIndex=$index"
        }

        // 编辑任务的路由
        fun createEditTaskRoute(taskData: String, index: Int): String {
            val encodedData = java.net.URLEncoder.encode(taskData, "UTF-8")
            return "unified_configuration/TASK?editData=$encodedData&editIndex=$index"
        }
    }

    // 详细配置界面（全屏配置界面）
    object DetailedConfiguration : Screen(
        route = "detailed_configuration/{configurationMode}/{itemType}?editData={editData}&editIndex={editIndex}",
        titleResId = R.string.detailed_configuration,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 创建详细配置路由
        fun createRoute(
            configurationMode: String,
            itemType: String,
            editData: String? = null,
            editIndex: Int? = null
        ): String {
            return if (editData != null && editIndex != null) {
                val encodedData = java.net.URLEncoder.encode(editData, "UTF-8")
                "detailed_configuration/$configurationMode/$itemType?editData=$encodedData&editIndex=$editIndex"
            } else {
                "detailed_configuration/$configurationMode/$itemType"
            }
        }
    }

    // 联系人选择界面
    object ContactSelection : Screen(
        route = "contact_selection/{selectionMode}?selectedContactIds={selectedContactIds}",
        titleResId = R.string.contact_selection,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 创建单选联系人路由
        fun createSingleSelectionRoute(): String {
            return "contact_selection/SINGLE"
        }

        // 创建多选联系人路由
        fun createMultiSelectionRoute(selectedContactIds: List<String> = emptyList()): String {
            return if (selectedContactIds.isNotEmpty()) {
                val encodedIds = java.net.URLEncoder.encode(selectedContactIds.joinToString(","), "UTF-8")
                "contact_selection/MULTI?selectedContactIds=$encodedIds"
            } else {
                "contact_selection/MULTI"
            }
        }
    }

    // 联系人分组选择界面
    object ContactGroupSelection : Screen(
        route = "contact_group_selection/{selectionMode}?selectedGroupId={selectedGroupId}",
        titleResId = R.string.contact_selection, // 复用联系人选择的字符串资源
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 创建单选联系人分组路由
        fun createSingleSelectionRoute(selectedGroupId: String = ""): String {
            return if (selectedGroupId.isNotEmpty()) {
                val encodedId = java.net.URLEncoder.encode(selectedGroupId, "UTF-8")
                "contact_group_selection/SINGLE?selectedGroupId=$encodedId"
            } else {
                "contact_group_selection/SINGLE"
            }
        }
    }

    // 应用选择界面
    object AppSelection : Screen(
        route = "app_selection/{selectionMode}?selectedAppPackageNames={selectedAppPackageNames}&filterType={filterType}&resultKey={resultKey}",
        titleResId = R.string.app_selection,
        selectedIcon = Icons.Filled.Apps,
        unselectedIcon = Icons.Outlined.Apps
    ) {
        // 创建单选应用路由
        fun createSingleSelectionRoute(filterType: String? = null, resultKey: String = "selected_apps"): String {
            return buildString {
                append("app_selection/SINGLE")
                val params = mutableListOf<String>()
                if (filterType != null) {
                    params.add("filterType=$filterType")
                }
                if (resultKey != "selected_apps") {
                    params.add("resultKey=$resultKey")
                }
                if (params.isNotEmpty()) {
                    append("?${params.joinToString("&")}")
                }
            }
        }

        // 创建多选应用路由
        fun createMultiSelectionRoute(
            selectedAppPackageNames: List<String> = emptyList(),
            resultKey: String = "selected_apps"
        ): String {
            return buildString {
                append("app_selection/MULTI")
                val params = mutableListOf<String>()
                if (selectedAppPackageNames.isNotEmpty()) {
                    val encodedIds = java.net.URLEncoder.encode(selectedAppPackageNames.joinToString(","), "UTF-8")
                    params.add("selectedAppPackageNames=$encodedIds")
                }
                if (resultKey != "selected_apps") {
                    params.add("resultKey=$resultKey")
                }
                if (params.isNotEmpty()) {
                    append("?${params.joinToString("&")}")
                }
            }
        }

        // 创建动态壁纸应用单选路由
        fun createLiveWallpaperSingleSelectionRoute(): String {
            return "app_selection/SINGLE?filterType=LIVE_WALLPAPER"
        }

        // 创建数字助理应用单选路由
        fun createDigitalAssistantSingleSelectionRoute(): String {
            return "app_selection/SINGLE?filterType=DIGITAL_ASSISTANT"
        }

        // 创建键盘应用单选路由
        fun createKeyboardSingleSelectionRoute(): String {
            return "app_selection/SINGLE?filterType=KEYBOARD"
        }
    }

    // 铃声选择界面
    object RingtoneSelection : Screen(
        route = "ringtone_selection/{ringtoneType}?selectedRingtoneUri={selectedRingtoneUri}",
        titleResId = R.string.ringtone_selection,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 创建铃声选择路由
        fun createRoute(
            ringtoneType: String,
            selectedRingtoneUri: String = ""
        ): String {
            return if (selectedRingtoneUri.isNotEmpty()) {
                val encodedUri = java.net.URLEncoder.encode(selectedRingtoneUri, "UTF-8")
                "ringtone_selection/$ringtoneType?selectedRingtoneUri=$encodedUri"
            } else {
                "ringtone_selection/$ringtoneType"
            }
        }
    }

    // 高级内存配置界面
    object AdvancedMemoryConfig : Screen(
        route = "advanced_memory_config/{mode}/{conditionId}",
        titleResId = R.string.advanced_memory_config,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 创建高级内存配置路由
        fun createRoute(mode: String, conditionId: String): String {
            return "advanced_memory_config/$mode/$conditionId"
        }
    }

    // 内存学习数据界面
    object MemoryLearningData : Screen(
        route = "memory_learning_data",
        titleResId = R.string.memory_learning_data,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        fun createRoute(): String {
            return "memory_learning_data"
        }
    }

    // 账号选择界面
    object AccountSelection : Screen(
        route = "account_selection/{selectionMode}?selectedAccountNames={selectedAccountNames}",
        titleResId = R.string.account_selection,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 创建单选账号路由
        fun createSingleSelectionRoute(): String {
            return "account_selection/SINGLE"
        }

        // 创建多选账号路由
        fun createMultiSelectionRoute(selectedAccountNames: List<String> = emptyList()): String {
            return if (selectedAccountNames.isNotEmpty()) {
                val encodedNames = java.net.URLEncoder.encode(selectedAccountNames.joinToString(","), "UTF-8")
                "account_selection/MULTI?selectedAccountNames=$encodedNames"
            } else {
                "account_selection/MULTI"
            }
        }
    }

    // 秒表选择界面
    object StopwatchSelection : Screen(
        route = "stopwatch_selection/{selectionMode}?selectedStopwatchIds={selectedStopwatchIds}",
        titleResId = R.string.stopwatch_selection,
        selectedIcon = Icons.Filled.Timer,
        unselectedIcon = Icons.Outlined.Timer
    ) {
        // 创建单选秒表路由
        fun createSingleSelectionRoute(): String {
            return "stopwatch_selection/SINGLE"
        }

        // 创建多选秒表路由
        fun createMultiSelectionRoute(selectedStopwatchIds: List<String> = emptyList()): String {
            return if (selectedStopwatchIds.isNotEmpty()) {
                val encodedIds = java.net.URLEncoder.encode(selectedStopwatchIds.joinToString(","), "UTF-8")
                "stopwatch_selection/MULTI?selectedStopwatchIds=$encodedIds"
            } else {
                "stopwatch_selection/MULTI"
            }
        }
    }

    // 分享目标选择界面
    object ShareTargetSelection : Screen(
        route = "share_target_selection",
        titleResId = R.string.share_target_selection,
        selectedIcon = Icons.Filled.Share,
        unselectedIcon = Icons.Outlined.Share
    ) {
        // 创建分享目标选择路由
        fun createRoute(): String {
            return "share_target_selection"
        }
    }

    // 应用重要性管理界面
    object AppImportanceManagement : Screen(
        route = "app_importance_management",
        titleResId = R.string.app_importance_management,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 创建应用重要性管理路由
        fun createRoute(): String {
            return "app_importance_management"
        }
    }

    // 高级清理策略配置界面
    object AdvancedCleanupStrategy : Screen(
        route = "advanced_cleanup_strategy/{strategyId}",
        titleResId = R.string.advanced_cleanup_strategy,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 创建高级清理策略配置路由
        fun createRoute(strategyId: String? = null): String {
            return "advanced_cleanup_strategy/${strategyId ?: "new"}"
        }
    }

    // 添加清理规则界面
    object AddCleanupRule : Screen(
        route = "add_cleanup_rule",
        titleResId = R.string.add_cleanup_rule,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        fun createRoute(): String {
            return "add_cleanup_rule"
        }
    }

    // 手势录制编辑界面
    object GestureRecordingEdit : Screen(
        route = "gesture_recording_edit/{recordingId}",
        titleResId = R.string.gesture_recording_edit,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        // 创建手势录制编辑路由
        fun createRoute(recordingId: String): String {
            return "gesture_recording_edit/$recordingId"
        }
    }

    // 智慧提醒详细配置界面（通用）
    object SmartReminderDetailConfig : Screen(
        route = "smart_reminder_detail_config/{reminderTypeId}?initialConfig={initialConfig}",
        titleResId = R.string.smart_reminder_detail_config,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        fun createRoute(reminderTypeId: String, initialConfig: String? = null): String {
            return if (initialConfig != null) {
                "smart_reminder_detail_config/$reminderTypeId?initialConfig=$initialConfig"
            } else {
                "smart_reminder_detail_config/$reminderTypeId"
            }
        }
    }

    // 自定义购物平台配置界面
    object CustomShoppingPlatformConfig : Screen(
        route = "custom_shopping_platform_config?platformData={platformData}",
        titleResId = R.string.custom_shopping_platform_config,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        fun createNewRoute(): String {
            return "custom_shopping_platform_config"
        }

        fun createEditRoute(platformData: String): String {
            val encodedData = java.net.URLEncoder.encode(platformData, "UTF-8")
            return "custom_shopping_platform_config?platformData=$encodedData"
        }
    }

    // 自定义应用平台配置界面
    object CustomAppPlatformConfig : Screen(
        route = "custom_app_platform_config?platformData={platformData}",
        titleResId = R.string.custom_app_platform_config,
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    ) {
        fun createNewRoute(): String {
            return "custom_app_platform_config"
        }

        fun createEditRoute(platformData: String): String {
            val encodedData = java.net.URLEncoder.encode(platformData, "UTF-8")
            return "custom_app_platform_config?platformData=$encodedData"
        }
    }


}

/**
 * 底部导航栏项目列表
 */
val bottomNavItems = listOf(
    Screen.PhoneCheckup,
    Screen.QuickCommands,
    Screen.CommandTemplates,
    Screen.SmartReminders,
    Screen.GlobalSettings
)

/**
 * 底部导航栏组件
 */
@Composable
fun BottomNavBar(
    navController: NavController,
    experimentalFeatureDetector: ExperimentalFeatureDetector? = null
) {
    NavigationBar {
        val navBackStackEntry by navController.currentBackStackEntryAsState()
        val currentDestination = navBackStackEntry?.destination

        bottomNavItems.forEach { screen ->
            val selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true

            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = if (selected) screen.selectedIcon else screen.unselectedIcon,
                        contentDescription = stringResource(id = screen.titleResId),
                        tint = if (selected) androidx.compose.material3.MaterialTheme.colorScheme.onSurface else UnselectedIconGray
                    )
                },
                label = { Text(stringResource(id = screen.titleResId)) },
                selected = selected,
                onClick = {
                    // 如果是全局设置导航项且有实验性功能检测器，处理点击检测
                    if (screen == Screen.GlobalSettings && experimentalFeatureDetector != null) {
                        experimentalFeatureDetector.handleClick(ExperimentalFeatureDetector.ClickTarget.NAVIGATION_ITEM)
                    }

                    navController.navigate(screen.route) {
                        // 避免创建多个返回栈
                        popUpTo(navController.graph.findStartDestination().id) {
                            saveState = true
                        }
                        // 避免多次点击创建多个实例
                        launchSingleTop = true
                        // 恢复状态
                        restoreState = true
                    }
                }
            )
        }
    }
}

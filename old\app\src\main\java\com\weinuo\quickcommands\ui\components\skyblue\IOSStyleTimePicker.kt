package com.weinuo.quickcommands.ui.components.skyblue

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.DialogSpacingConfigurationManager
import com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager
import kotlinx.coroutines.launch

/**
 * iOS风格的时间选择器组件
 * 类似于时钟应用中的时间调整界面，支持上下拨动选择时间
 */
@Composable
fun IOSStyleTimePicker(
    hour: Int,
    minute: Int,
    onTimeChange: (hour: Int, minute: Int) -> Unit,
    modifier: Modifier = Modifier,
    settingsRepository: SettingsRepository
) {
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 小时选择器
            TimePickerColumn(
                value = hour,
                range = 0..23,
                onValueChange = { newHour -> onTimeChange(newHour, minute) },
                modifier = Modifier.weight(1f),
                settingsRepository = settingsRepository
            )

            // 分钟选择器
            TimePickerColumn(
                value = minute,
                range = 0..59,
                onValueChange = { newMinute -> onTimeChange(hour, newMinute) },
                modifier = Modifier.weight(1f),
                settingsRepository = settingsRepository
            )
        }

        // 连接的分割线 - 上分割线
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(1.dp)
                .offset(y = 40.dp)
                .background(Color(0xFFE0E0E0))
        )

        // 连接的分割线 - 下分割线
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(1.dp)
                .offset(y = 80.dp)
                .background(Color(0xFFE0E0E0))
        )
    }
}

/**
 * 时间选择器的单列组件
 * 支持上下滚动选择数值
 */
@Composable
private fun TimePickerColumn(
    value: Int,
    range: IntRange,
    onValueChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
    settingsRepository: SettingsRepository
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    val uiSpacingConfigManager = remember { UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()

    // 创建数值列表，包含当前值前后的数值以便滚动
    val values = range.toList()
    val currentIndex = values.indexOf(value)

    // 只在初始化时滚动到当前值，避免无限循环
    LaunchedEffect(Unit) {
        if (currentIndex >= 0) {
            println("TimePickerColumn: Initial scroll to index $currentIndex for value $value")
            listState.scrollToItem(currentIndex)
        }
    }

    // 监听滚动状态变化，但只在滚动停止时更新选中值
    var isScrolling by remember { mutableStateOf(false) }
    LaunchedEffect(listState.isScrollInProgress) {
        if (listState.isScrollInProgress) {
            isScrolling = true
        } else if (isScrolling) {
            // 滚动刚停止，更新选中值
            isScrolling = false
            val visibleItemInfo = listState.layoutInfo.visibleItemsInfo
            if (visibleItemInfo.isNotEmpty()) {
                // 分割线区域：考虑LazyColumn的contentPadding
                // LazyColumn有40dp的上下padding，所以实际的中心区域应该是相对于LazyColumn内容的
                // 组件高度120dp，LazyColumn内容区域是40dp，中心应该在20dp附近
                val contentPadding = with(density) { 40.dp.toPx() }
                val contentHeight = with(density) { 40.dp.toPx() } // 120dp - 40dp上 - 40dp下 = 40dp
                val centerY = contentHeight / 2
                val selectionAreaTop = centerY - with(density) { 15.dp.toPx() }
                val selectionAreaBottom = centerY + with(density) { 15.dp.toPx() }

                println("TimePickerColumn: Content padding: $contentPadding, content height: $contentHeight, center: $centerY")
                println("TimePickerColumn: Selection area: $selectionAreaTop - $selectionAreaBottom")
                println("TimePickerColumn: Visible items count: ${visibleItemInfo.size}")

                // 找到在分割线区域内的项目
                val itemInSelectionArea = visibleItemInfo.find { itemInfo ->
                    val itemCenter = itemInfo.offset + itemInfo.size / 2
                    println("TimePickerColumn: Item ${itemInfo.index} center: $itemCenter, offset: ${itemInfo.offset}, size: ${itemInfo.size}")
                    itemCenter >= selectionAreaTop && itemCenter <= selectionAreaBottom
                }

                // 找到最接近中心的项目并自动吸附
                val snapCenterY = with(density) { 20.dp.toPx() } // LazyColumn内容区域的中心
                var closestItem: androidx.compose.foundation.lazy.LazyListItemInfo? = null
                var minDistance = Float.MAX_VALUE

                visibleItemInfo.forEach { itemInfo ->
                    val itemCenter = itemInfo.offset + itemInfo.size / 2
                    val distance = kotlin.math.abs(itemCenter - snapCenterY)
                    if (distance < minDistance) {
                        minDistance = distance
                        closestItem = itemInfo
                    }
                }

                closestItem?.let { item ->
                    val newValue = values.getOrNull(item.index)
                    val itemCenter = item.offset + item.size / 2
                    println("TimePickerColumn: Closest item: index=${item.index}, value=$newValue, center=$itemCenter, distance=$minDistance")

                    if (newValue != null && newValue != value) {
                        println("TimePickerColumn: Updating value from $value to $newValue")
                        onValueChange(newValue)
                    }

                    // 自动吸附到中心：如果项目不在正中心，则滚动到中心
                    if (minDistance > with(density) { 2.dp.toPx() }) { // 允许2dp的误差
                        coroutineScope.launch {
                            println("TimePickerColumn: Auto-snapping to center for item ${item.index}")
                            listState.animateScrollToItem(item.index)
                        }
                    }
                } ?: run {
                    println("TimePickerColumn: No item found for snapping")
                }
            }
        }
    }

    Box(
        modifier = modifier
            .height(120.dp),
        contentAlignment = Alignment.Center
    ) {
        println("TimePickerColumn: Box height = 120dp = ${with(density) { 120.dp.toPx() }}px")
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = 40.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            itemsIndexed(values) { index, timeValue ->
                val isSelected = timeValue == value

                Text(
                    text = String.format("%02d", timeValue),
                    fontSize = if (isSelected) 20.sp else 16.sp,
                    fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                    color = if (isSelected) Color(0xFF0A59F7) else Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            println("TimePickerColumn: Clicked on value $timeValue at index $index")
                            onValueChange(timeValue)
                            coroutineScope.launch {
                                listState.animateScrollToItem(index)
                            }
                        }
                        .padding(vertical = 8.dp)
                )
            }
        }
    }
}

/**
 * 带标题的时间选择器组件
 * 包含开始时间和结束时间的完整选择界面
 */
@Composable
fun IOSStyleTimeRangePicker(
    startHour: Int,
    startMinute: Int,
    endHour: Int,
    endMinute: Int,
    onStartTimeChange: (hour: Int, minute: Int) -> Unit,
    onEndTimeChange: (hour: Int, minute: Int) -> Unit,
    modifier: Modifier = Modifier,
    settingsRepository: SettingsRepository
) {
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(uiSpacingConfig.settingsItemSpacing.dp)
    ) {
        // 开始时间标题和选择器
        Text(
            text = "开始时间",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Medium
        )

        IOSStyleTimePicker(
            hour = startHour,
            minute = startMinute,
            onTimeChange = onStartTimeChange,
            settingsRepository = settingsRepository
        )

        Spacer(modifier = Modifier.height(uiSpacingConfig.settingsItemSpacing.dp))

        // 结束时间标题和选择器
        Text(
            text = "结束时间",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Medium
        )

        IOSStyleTimePicker(
            hour = endHour,
            minute = endMinute,
            onTimeChange = onEndTimeChange,
            settingsRepository = settingsRepository
        )
    }
}

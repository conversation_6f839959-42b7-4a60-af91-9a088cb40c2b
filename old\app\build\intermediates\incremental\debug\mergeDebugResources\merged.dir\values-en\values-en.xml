<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="accessibility_service_description">Enable \"Quick Commands - System Priority Enhancement\" service in system accessibility settings to improve app background survival rate</string>
    <string name="accessibility_service_disable">Disable</string>
    <string name="accessibility_service_enable">Enable</string>
    <string name="accessibility_service_go_to_settings">Go to Settings</string>
    <string name="accessibility_service_manual_title">Manual System Priority Enhancement</string>
    <string name="accessibility_service_name">Quick Commands - System Priority Enhancement</string>
    <string name="accessibility_service_system_description">Improve app system priority and optimize background survival rate</string>
    <string name="accessibility_service_title">System Priority Enhancement</string>
    <string name="account_selection">Select Account</string>
    <string name="add_cleanup_rule">Add Cleanup Rule</string>
    <string name="add_cleanup_rule_description">Add new cleanup rule</string>
    <string name="address_reminder_description">Suggest opening map app when copying address</string>
    <string name="address_reminder_message">Address detected, suggest opening \"%1$s\"</string>
    <string name="address_reminder_status_disabled">Disabled</string>
    <string name="address_reminder_status_enabled">Monitoring address copying - %1$s</string>
    <string name="address_reminder_title">Address Reminder</string>
    <string name="advanced_cleanup_strategy">Advanced Cleanup Strategy Configuration</string>
    <string name="advanced_cleanup_strategy_description">Customize cleanup rules and order</string>
    <string name="airplane_mode_changed">Airplane Mode</string>
    <string name="airplane_mode_changed_description">Triggered when airplane mode state changes</string>
    <string name="ambient_display">Ambient Display</string>
    <string name="ambient_display_description">Configure ambient display mode</string>
    <string name="app_detection_any_app">Any App</string>
    <string name="app_detection_selected_apps">Selected Apps</string>
    <string name="app_execute_javascript">Execute JavaScript Code</string>
    <string name="app_execute_javascript_description">Execute custom JavaScript code</string>
    <string name="app_execute_shell_script">Execute Shell Script</string>
    <string name="app_execute_shell_script_description">Execute custom Shell script</string>
    <string name="app_force_stop_app">Force Stop App</string>
    <string name="app_force_stop_app_description">Force stop specified application</string>
    <string name="app_freeze_app">Freeze App</string>
    <string name="app_freeze_app_description">Freeze specified application</string>
    <string name="app_importance_management">App Importance Management</string>
    <string name="app_importance_management_description">Manage app importance settings</string>
    <string name="app_interface_click">Interface Click</string>
    <string name="app_interface_click_description">Trigger condition when specific text content is clicked</string>
    <string name="app_launch_app">Launch App</string>
    <string name="app_launch_app_description">Launch specified application</string>
    <string name="app_lifecycle">Lifecycle</string>
    <string name="app_lifecycle_description">App launch, close</string>
    <string name="app_link_reminder_description">Intelligently detect app links and suggest opening the corresponding application</string>
    <string name="app_link_reminder_status_disabled">Disabled</string>
    <string name="app_link_reminder_status_enabled">Monitoring clipboard</string>
    <string name="app_link_reminder_status_unconfigured">Feature ready to use</string>
    <string name="app_link_reminder_title">Open App Links</string>
    <string name="app_management">App Management</string>
    <string name="app_management_description">Launch, stop, install, uninstall apps</string>
    <string name="app_name">Quick Commands</string>
    <string name="app_open_website">Open Website</string>
    <string name="app_open_website_description">Open specified website in default browser</string>
    <string name="app_package_management">Package Management</string>
    <string name="app_package_management_description">App install, delete, update</string>
    <string name="app_screen_content">Screen Content</string>
    <string name="app_screen_content_description">Trigger condition when certain text content appears or disappears on screen</string>
    <string name="app_selection">Select App</string>
    <string name="app_state">App State</string>
    <string name="app_state_change">State Change</string>
    <string name="app_state_change_description">App foreground/background state changes, background time exceeds threshold</string>
    <string name="app_state_description">App foreground, background, installation</string>
    <string name="app_tasker_locale_plugin">Tasker/Locale Plugin</string>
    <string name="app_tasker_locale_plugin_description">Execute Tasker or Locale task plugin</string>
    <string name="app_trigger_all_apps">All Apps</string>
    <string name="app_trigger_any_app">Any App</string>
    <string name="app_unfreeze_app">Unfreeze App</string>
    <string name="app_unfreeze_app_description">Unfreeze specified application</string>
    <string name="application_task">Application Task</string>
    <string name="application_task_description">Launch apps, force stop</string>
    <string name="auto_clicker_accessibility_service_description">Used for auto clicker functionality including: Recording user touch operations (tap, swipe, long press, etc.), Playing back recorded operation sequences, Managing recording files (save, load, edit). Please enable this service if you need to use auto clicker related features.</string>
    <string name="auto_clicker_accessibility_service_name">Quick Commands - Auto Clicker Service</string>
    <string name="auto_rotate_disable">Disable</string>
    <string name="auto_rotate_enable">Enable</string>
    <string name="auto_sync_state">Auto Sync</string>
    <string name="auto_sync_state_description">Triggered when auto sync state changes</string>
    <string name="back">Back</string>
    <string name="batch_delete_confirm">Are you sure you want to delete the selected %1$d quick commands? This action will:</string>
    <string name="batch_delete_quick_commands">Batch Delete Quick Commands</string>
    <string name="batch_delete_warning">• Remove these quick commands from the app</string>
    <string name="battery_battery_level">Battery Level</string>
    <string name="battery_battery_level_description">Monitor battery level changes and thresholds</string>
    <string name="battery_charging_state">Charging State</string>
    <string name="battery_charging_state_description">Monitor device charging state changes</string>
    <string name="battery_state">Battery State</string>
    <string name="battery_state_description">Battery level, charging state changes</string>
    <string name="camera_flashlight_control">Flashlight Control</string>
    <string name="camera_flashlight_control_description">Control flashlight enable, disable or toggle</string>
    <string name="camera_open_last_photo">Open Last Photo</string>
    <string name="camera_open_last_photo_description">Quick access to the last photo in gallery</string>
    <string name="camera_record_video">Record Video</string>
    <string name="camera_record_video_description">Record video using front or rear camera, supports timed recording and custom save location</string>
    <string name="camera_screenshot">Screenshot</string>
    <string name="camera_screenshot_description">Capture current screen content and save</string>
    <string name="camera_take_photo">Take Photo</string>
    <string name="camera_take_photo_description">Take photo using front or rear camera, supports custom save location</string>
    <string name="camera_task">Photo Viewer</string>
    <string name="camera_task_description">View photos, screenshot</string>
    <string name="cancel">Cancel</string>
    <string name="change_map_apps">Change Map Apps</string>
    <string name="change_music_apps">Change Music Apps</string>
    <string name="check_text_content">Text Content to Check</string>
    <string name="clipboard_changed">Clipboard Changed</string>
    <string name="clipboard_changed_description">Triggered when clipboard content changes</string>
    <string name="comm_call_active">Call Active</string>
    <string name="comm_call_active_description">Trigger condition when call is active</string>
    <string name="comm_call_ended">Call Ended</string>
    <string name="comm_call_ended_description">Trigger condition when call ends</string>
    <string name="comm_incoming_call">Incoming Call</string>
    <string name="comm_incoming_call_description">Trigger condition when incoming call</string>
    <string name="comm_outgoing_call">Outgoing Call</string>
    <string name="comm_outgoing_call_description">Trigger condition when making call</string>
    <string name="comm_sms_received">SMS Received</string>
    <string name="comm_sms_received_description">Trigger condition when SMS is received</string>
    <string name="comm_sms_sent">SMS Sent</string>
    <string name="comm_sms_sent_description">Trigger condition when SMS is sent</string>
    <string name="communication_state">Communication State</string>
    <string name="communication_state_description">Call and SMS state changes</string>
    <string name="configure_item">Configure %1$s</string>
    <string name="confirm_configuration">Confirm Configuration</string>
    <string name="conn_bluetooth_state">Bluetooth State</string>
    <string name="conn_bluetooth_state_description">Trigger condition when Bluetooth is enabled/disabled or connected/disconnected</string>
    <string name="conn_mobile_data">Mobile Data</string>
    <string name="conn_mobile_data_description">Trigger condition when mobile data connection state changes</string>
    <string name="conn_wifi_network">WiFi Network</string>
    <string name="conn_wifi_network_description">Trigger condition when connected to specific WiFi network</string>
    <string name="conn_wifi_state">WiFi State</string>
    <string name="conn_wifi_state_description">Trigger condition when WiFi is enabled/disabled or connected/disconnected</string>
    <string name="connection_state">Connection State</string>
    <string name="connection_state_description">Network, Bluetooth, USB connection states</string>
    <string name="connectivity_airplane_mode_control">Airplane Mode Control</string>
    <string name="connectivity_airplane_mode_control_description">Control airplane mode enable, disable or toggle</string>
    <string name="connectivity_bluetooth_control">Bluetooth Control</string>
    <string name="connectivity_bluetooth_control_description">Control Bluetooth enable, disable or toggle</string>
    <string name="connectivity_hotspot_control">Hotspot Control</string>
    <string name="connectivity_hotspot_control_description">Control WiFi hotspot enable, disable or toggle</string>
    <string name="connectivity_mobile_data_control">Mobile Data Control</string>
    <string name="connectivity_mobile_data_control_description">Control mobile data enable, disable or toggle</string>
    <string name="connectivity_network_check">Network Connection Check</string>
    <string name="connectivity_network_check_description">Check network connection status and return result</string>
    <string name="connectivity_nfc_control">NFC Control</string>
    <string name="connectivity_nfc_control_description">Control NFC enable, disable or toggle</string>
    <string name="connectivity_send_intent">Send Intent</string>
    <string name="connectivity_send_intent_description">Send custom Intent to specified app or system</string>
    <string name="connectivity_task">Connectivity Task</string>
    <string name="connectivity_task_description">WiFi, Bluetooth, network control</string>
    <string name="connectivity_wifi_control">WiFi Control</string>
    <string name="connectivity_wifi_control_description">Control WiFi enable, disable or toggle</string>
    <string name="contact_selection">Select Contact</string>
    <string name="custom_app_platform_config">Custom App Platform Config</string>
    <string name="dark_theme_changed">Dark Theme</string>
    <string name="dark_theme_changed_description">System dark theme state changes</string>
    <string name="datetime_alarm">Alarm Operation</string>
    <string name="datetime_alarm_description">Set, cancel alarm, supports custom ringtone</string>
    <string name="datetime_stopwatch">Stopwatch Operation</string>
    <string name="datetime_stopwatch_description">Start, stop, reset stopwatch</string>
    <string name="datetime_task">Date Time</string>
    <string name="datetime_task_description">Time settings, alarms, calendar</string>
    <string name="default_keyboard">Keyboard - Set Default</string>
    <string name="default_keyboard_description">Set default input method keyboard</string>
    <string name="delete">Delete</string>
    <string name="delete_operation_irreversible">This action cannot be undone.</string>
    <string name="delete_quick_command">Delete Quick Command</string>
    <string name="delete_quick_command_confirm">Are you sure you want to delete quick command \"%1$s\"? This action will:</string>
    <string name="delete_quick_command_warning">• Remove this quick command from the app</string>
    <string name="demo_mode">Demo Mode</string>
    <string name="demo_mode_description">Control system demo mode</string>
    <string name="detailed_configuration">Detailed Configuration</string>
    <string name="device_action_share_text">Share Text</string>
    <string name="device_action_share_text_description">Share text content through system share function</string>
    <string name="device_action_task">Device Action</string>
    <string name="device_action_task_description">Share, clipboard, system operations</string>
    <string name="device_boot_completed">Device Boot</string>
    <string name="device_boot_completed_description">Triggered when device boot completes</string>
    <string name="device_clipboard_changed">Clipboard Changed</string>
    <string name="device_clipboard_changed_description">Trigger condition when clipboard content changes</string>
    <string name="device_event">Device Event</string>
    <string name="device_event_description">Battery, screen, system events</string>
    <string name="device_gps_state">GPS State</string>
    <string name="device_gps_state_description">Trigger condition when GPS state changes</string>
    <string name="device_logcat_message">Logcat Message</string>
    <string name="device_logcat_message_description">Trigger condition when specified message appears in Logcat</string>
    <string name="device_settings">Device Settings</string>
    <string name="device_settings_accessibility_service">Accessibility Service</string>
    <string name="device_settings_accessibility_service_description">Control the enable status of specified accessibility service</string>
    <string name="device_settings_auto_rotate">Auto Rotate</string>
    <string name="device_settings_auto_rotate_description">Control screen auto rotation function</string>
    <string name="device_settings_description">System settings, themes, wallpapers, invert colors and other device-level configurations</string>
    <string name="device_settings_display_density">Display Density</string>
    <string name="device_settings_display_density_description">Adjust screen display density percentage</string>
    <string name="device_settings_driving_mode">Driving Mode</string>
    <string name="device_settings_driving_mode_description">Control driving mode function</string>
    <string name="device_settings_enter_screensaver">Enter Screensaver</string>
    <string name="device_settings_enter_screensaver_description">Activate system screensaver mode</string>
    <string name="device_settings_font_size">Font Size</string>
    <string name="device_settings_font_size_description">Adjust system font size percentage</string>
    <string name="device_settings_immersive_mode">Immersive Mode</string>
    <string name="device_settings_immersive_mode_description">Set system immersive mode type</string>
    <string name="device_settings_invert_colors">Invert Colors</string>
    <string name="device_settings_invert_colors_description">Toggle screen color inversion mode</string>
    <string name="device_settings_keyboard_hint">Keyboard Hint</string>
    <string name="device_settings_keyboard_hint_description">Control keyboard hint function</string>
    <string name="dialog_content">Dialog Content</string>
    <string name="dialog_content_placeholder">Enter dialog content</string>
    <string name="dialog_settings">Show Dialog Settings</string>
    <string name="dialog_title">Dialog Title</string>
    <string name="dialog_title_placeholder">Enter dialog title</string>
    <string name="digital_assistant">Set Digital Assistant</string>
    <string name="digital_assistant_description">Set default digital assistant app</string>
    <string name="disable">Disable</string>
    <string name="dock_state">Dock State</string>
    <string name="dock_state_description">Device dock connection state changes</string>
    <string name="driving_mode">Driving Mode</string>
    <string name="driving_mode_description">Control driving mode functionality</string>
    <string name="edit_abort_condition">Edit Abort Condition</string>
    <string name="edit_abort_condition_description">Modify abort condition parameters</string>
    <string name="edit_condition">Edit Condition</string>
    <string name="edit_condition_description">Modify trigger condition parameters</string>
    <string name="edit_task">Edit Task</string>
    <string name="edit_task_description">Modify task parameters</string>
    <string name="enable">Enable</string>
    <string name="executing_quick_command">Executing quick command: %1$s (%2$d tasks)</string>
    <string name="execution_mode">Execution Mode</string>
    <string name="experimental_features">Experimental Features</string>
    <string name="experimental_features_description">Enabling experimental features may affect app stability, please use with caution</string>
    <string name="experimental_features_enabled">Enable Experimental Features</string>
    <string name="file_file_operation">File Management</string>
    <string name="file_file_operation_description">File management operations: copy, move, delete, compress, create folder, rename</string>
    <string name="file_open_file">Open File</string>
    <string name="file_open_file_description">Open specified file, supports manual input or file selection, can specify app</string>
    <string name="file_operation_task">File Read/Write</string>
    <string name="file_operation_task_description">File read/write, open files</string>
    <string name="file_write_file">Write File</string>
    <string name="file_write_file_description">Write content to specified file, supports append, overwrite, prepare commit modes</string>
    <string name="font_size">Font Size</string>
    <string name="font_size_description">Adjust system font size percentage</string>
    <string name="font_size_percentage">Font Size Percentage</string>
    <string name="font_size_settings">Font Size Settings</string>
    <string name="font_weight_bold">Bold</string>
    <string name="font_weight_medium">Medium</string>
    <string name="font_weight_regular">Regular</string>
    <string name="font_weight_selection_title">Select Font Weight</string>
    <string name="gesture_recognition_accessibility_service_description">Used for gesture recognition including: Fingerprint gesture trigger conditions (triggered when you perform specified fingerprint gestures). Please enable this service if you need to use gesture-related trigger conditions.</string>
    <string name="gesture_recognition_accessibility_service_name">Quick Commands - Gesture Recognition Service</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="gps_state">GPS State</string>
    <string name="gps_state_description">Triggered when GPS state changes</string>
    <string name="icon_weight_bold">Bold</string>
    <string name="icon_weight_medium">Medium</string>
    <string name="icon_weight_regular">Regular</string>
    <string name="icon_weight_selection_title">Select Icon Weight</string>
    <string name="info_message_ringtone">Message Ringtone Settings</string>
    <string name="info_message_ringtone_description">Set ringtone for SMS and notifications</string>
    <string name="info_send_email">Send Email</string>
    <string name="info_send_email_description">Send email to specified address, supports complete SMTP configuration and notification options</string>
    <string name="info_send_sms">Send SMS</string>
    <string name="info_send_sms_description">Send SMS to specified number, supports SIM card selection and pre-fill mode</string>
    <string name="info_show_dialog">Show Dialog</string>
    <string name="info_show_dialog_description">Display custom dialog with title, content and button configuration support</string>
    <string name="info_show_toast">Show Toast</string>
    <string name="info_show_toast_description">Display brief message on screen</string>
    <string name="information_task">Information Task</string>
    <string name="information_task_description">SMS, email, notifications</string>
    <string name="intelligent_link_recognition_description">Automatically optimize link format to improve recognition accuracy and compatibility</string>
    <string name="intelligent_link_recognition_enabled">Enable Intelligent Recognition</string>
    <string name="intelligent_link_recognition_help">Automatically standardize link format and clean non-standard characters</string>
    <string name="intelligent_link_recognition_title">Intelligent Link Recognition</string>
    <string name="intent_received">Intent Received</string>
    <string name="intent_received_description">Triggered when specified Intent is received</string>
    <string name="interface_click">Interface Click</string>
    <string name="interface_click_description">Triggered when specific text content is clicked</string>
    <string name="interface_interaction_accessibility_service_description">Used for interface automation operations including: Interface click conditions (triggered when specific text content is clicked), Screen content conditions (triggered when certain text content appears or disappears on screen), Check screen text task (check if specific text string is currently displayed on screen, supports exact match/contains match, overlay detection, view ID extraction and other advanced options), Read screen content task (capture current screen content to text file), Check interface element color task (analyze color characteristics of interface elements at specified positions, infer color information based on element type, state, text content, etc.). Please enable this service if you need to use interface-related trigger conditions or tasks.</string>
    <string name="interface_interaction_accessibility_service_name">Quick Commands - Interface Interaction Service</string>
    <string name="interface_interaction_service_not_enabled">Interface interaction accessibility service not enabled, cannot execute screen text check task</string>
    <string name="invalid_command">Invalid command</string>
    <string name="invert_colors">Invert Colors</string>
    <string name="invert_colors_description">Toggle screen color inversion mode</string>
    <string name="invert_colors_off">Off</string>
    <string name="invert_colors_on">On</string>
    <string name="invert_colors_operation">Invert Colors Operation</string>
    <string name="keyboard_hint">Keyboard Hint</string>
    <string name="keyboard_hint_description">Control keyboard hint functionality</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_selection_title">Select Language</string>
    <string name="language_settings">Language Settings</string>
    <string name="language_settings_description">Select app display language</string>
    <string name="language_system_default">System Default</string>
    <string name="lifecycle">Lifecycle</string>
    <string name="lifecycle_description">App launch, close</string>
    <string name="location_force_location_update">Force Location Update</string>
    <string name="location_force_location_update_description">Force update current location information</string>
    <string name="location_get_location">Get Location</string>
    <string name="location_get_location_description">Get current location information and save to variable</string>
    <string name="location_set_location_update_frequency">Set Location Update Frequency</string>
    <string name="location_set_location_update_frequency_description">Set the frequency of location information updates</string>
    <string name="location_share_location">Share Location</string>
    <string name="location_share_location_description">Share current location via SMS, contact or phone number</string>
    <string name="location_task">Location Task</string>
    <string name="location_task_description">GPS, location service control</string>
    <string name="location_toggle_location_service">Location Service Control</string>
    <string name="location_toggle_location_service_description">Enable, disable or toggle location service status</string>
    <string name="logcat_message">Logcat Message</string>
    <string name="logcat_message_description">Triggered when specified message appears in Logcat</string>
    <string name="login_attempt_failed">Login Failed</string>
    <string name="login_attempt_failed_description">Triggered when device login attempt fails</string>
    <string name="manual_dynamic_shortcut">Dynamic Shortcut</string>
    <string name="manual_dynamic_shortcut_description">Trigger via dynamic shortcut</string>
    <string name="manual_fingerprint_gesture">Fingerprint Gesture</string>
    <string name="manual_fingerprint_gesture_description">Trigger via fingerprint gesture</string>
    <string name="manual_media_key_press">Media Key Press</string>
    <string name="manual_media_key_press_description">Triggered when media keys (on headphones) are pressed 1 to 3 times</string>
    <string name="manual_static_shortcut">Static Shortcut</string>
    <string name="manual_static_shortcut_description">Trigger via static shortcut</string>
    <string name="manual_trigger">Manual Trigger</string>
    <string name="manual_trigger_description">Shortcuts, widget triggers</string>
    <string name="manual_volume_key_press">Volume Key Press</string>
    <string name="manual_volume_key_press_description">Trigger via volume key press, with option to preserve original volume</string>
    <string name="manual_widget_update">Manual Widget Update</string>
    <string name="manual_widget_update_description">Immediately update all desktop widget content, regardless of whether auto-update is enabled</string>
    <string name="match_options">Match Options</string>
    <string name="media_microphone_recording">Microphone Recording</string>
    <string name="media_microphone_recording_description">Record audio with custom duration and format support</string>
    <string name="media_multimedia_control">Multimedia Control</string>
    <string name="media_multimedia_control_description">Simulate media buttons, default player control, audio buttons</string>
    <string name="media_play_stop_sound">Play/Stop Sound</string>
    <string name="media_play_stop_sound_description">Play audio file, ringtone or stop existing sound</string>
    <string name="media_task">Media Task</string>
    <string name="media_task_description">Media control, sound playback</string>
    <string name="music_app_reminder_description">Suggest opening music app when headphones connected</string>
    <string name="music_app_reminder_status_configured">Selected: %s</string>
    <string name="music_app_reminder_status_disabled">Disabled</string>
    <string name="music_app_reminder_status_enabled">Suggest opening when headphones connected: %s</string>
    <string name="music_app_reminder_status_unconfigured">Need to select music app</string>
    <string name="music_app_reminder_title">Music App Reminder</string>
    <string name="music_playback_state">Music Playback</string>
    <string name="music_playback_state_description">Triggered when music playback state changes</string>
    <string name="nav_command_templates">Explore</string>
    <string name="nav_global_settings">Settings</string>
    <string name="nav_phone_checkup">Checkup</string>
    <string name="nav_quick_commands">Commands</string>
    <string name="nav_smart_reminders">Reminders</string>
    <string name="no_quick_commands">No quick commands</string>
    <string name="no_search_results">No matching quick commands found</string>
    <string name="no_template_search_results">No matching templates found</string>
    <string name="no_templates">No templates available</string>
    <string name="notification_cancel_notification">Cancel Notification</string>
    <string name="notification_cancel_notification_description">Cancel specified notification</string>
    <string name="notification_event">Notification Event</string>
    <string name="notification_event_description">Triggered when notification is posted or cancelled</string>
    <string name="notification_listener_service_description">Used to listen to system notification events and implement notification-related automation features.</string>
    <string name="notification_listener_service_name">Quick Commands - Notification Listener</string>
    <string name="notification_show_notification">Show Notification</string>
    <string name="notification_show_notification_description">Display custom notification in notification bar</string>
    <string name="notification_task">Notification Task</string>
    <string name="notification_task_description">Send, cancel notifications</string>
    <string name="package_management">Package Management</string>
    <string name="package_management_description">App installation, removal, update</string>
    <string name="package_name">Package Name</string>
    <string name="phone_answer_call">Answer Call</string>
    <string name="phone_answer_call_description">Answer incoming call</string>
    <string name="phone_checkup_title">Checkup</string>
    <string name="phone_clear_call_log">Clear Call Log</string>
    <string name="phone_clear_call_log_description">Clear specified type of call log</string>
    <string name="phone_make_call">Make Call</string>
    <string name="phone_make_call_description">Dial specified phone number</string>
    <string name="phone_open_call_log">Open Call Log</string>
    <string name="phone_open_call_log_description">Open system call log page</string>
    <string name="phone_reject_call">Reject Call</string>
    <string name="phone_reject_call_description">Reject incoming call</string>
    <string name="phone_ringtone_settings">Phone Ringtone Settings</string>
    <string name="phone_ringtone_settings_description">Set phone ringtone, select custom ringtone and apply to system</string>
    <string name="phone_task">Phone Task</string>
    <string name="phone_task_description">Make calls, answer calls, etc.</string>
    <string name="power_save_mode">Power Save Mode</string>
    <string name="power_save_mode_description">Control system power save mode</string>
    <string name="quick_command_aborted">Quick command execution aborted: %1$s, abort conditions: %2$s</string>
    <string name="quick_command_completed">Quick command execution completed: %1$s</string>
    <string name="quick_command_edit">Edit Quick Command</string>
    <string name="quick_command_form">Quick Command Form</string>
    <string name="quick_command_new">New Quick Command</string>
    <string name="quick_command_not_found">Cannot find associated quick command</string>
    <string name="quick_command_not_found_simple">Quick command not found</string>
    <string name="quick_commands_title">Commands</string>
    <string name="ringer_mode_changed">Ringer Mode</string>
    <string name="ringer_mode_changed_description">Triggered when ringer mode changes</string>
    <string name="ringtone_selection">Select Ringtone</string>
    <string name="save">Save</string>
    <string name="screen_brightness_control">Brightness Control</string>
    <string name="screen_brightness_control_description">Adjust screen brightness, supports percentage and absolute value settings</string>
    <string name="screen_content">Screen Content</string>
    <string name="screen_content_description">Triggered when certain text content appears or disappears on screen</string>
    <string name="screen_content_text">Screen Content Text</string>
    <string name="screen_content_text_label">Screen text content to detect</string>
    <string name="screen_content_text_placeholder">e.g.: Loading, Network Error, Login Successful, etc.</string>
    <string name="screen_control_task">Screen Control</string>
    <string name="screen_control_task_description">Screen brightness, orientation, lock</string>
    <string name="screen_event_auto_rotate_disabled">Auto Rotate Disabled</string>
    <string name="screen_event_auto_rotate_enabled">Auto Rotate Enabled</string>
    <string name="screen_event_off">Screen Off</string>
    <string name="screen_event_on">Screen On</string>
    <string name="screen_event_unlocked">Screen Unlocked</string>
    <string name="screen_keep_device_awake">Keep Device Awake</string>
    <string name="screen_keep_device_awake_description">Prevent device from entering sleep state</string>
    <string name="screen_rotation_reminder_description">Suggest screen rotation after flipping phone</string>
    <string name="screen_rotation_reminder_status_disabled">Disabled</string>
    <string name="screen_rotation_reminder_status_enabled">Monitoring screen rotation</string>
    <string name="screen_rotation_reminder_status_unconfigured">Feature ready to use</string>
    <string name="screen_rotation_reminder_title">Screen Rotation Reminder</string>
    <string name="screen_state">Screen State</string>
    <string name="screen_state_description">Screen on, off, unlock state changes</string>
    <string name="screen_text_check_failed">Screen text check failed</string>
    <string name="search_apps">Search app name or package name</string>
    <string name="search_field_icon_weight">Search Icon Weight</string>
    <string name="search_field_icon_weight_description">Adjust the thickness of search field icon</string>
    <string name="search_field_placeholder_font_weight">Placeholder Font Weight</string>
    <string name="search_field_placeholder_font_weight_description">Adjust the thickness of search field placeholder text</string>
    <string name="search_field_settings">Search Field Settings</string>
    <string name="search_quick_commands">Search quick command names</string>
    <string name="search_smart_reminders">Search smart reminder features</string>
    <string name="search_templates">Search template names or descriptions</string>
    <string name="select_map_apps">Select Map Apps</string>
    <string name="select_music_apps">Select Music Apps</string>
    <string name="sensor_activity_recognition">Activity Recognition</string>
    <string name="sensor_activity_recognition_description">Trigger condition when specific activity state is detected</string>
    <string name="sensor_flip_sensor">Device Flip Detection</string>
    <string name="sensor_flip_sensor_description">Trigger condition when device is flipped</string>
    <string name="sensor_light_sensor">Light Sensor</string>
    <string name="sensor_light_sensor_description">Trigger condition when ambient light changes</string>
    <string name="sensor_orientation_sensor">Orientation Sensor</string>
    <string name="sensor_orientation_sensor_description">Trigger condition when screen orientation changes</string>
    <string name="sensor_proximity_sensor">Proximity Sensor</string>
    <string name="sensor_proximity_sensor_description">Trigger condition when object approaches or moves away</string>
    <string name="sensor_shake_sensor">Shake Detection</string>
    <string name="sensor_shake_sensor_description">Trigger condition when device is shaken</string>
    <string name="sensor_sleep_sensor">Sleep Detection</string>
    <string name="sensor_sleep_sensor_description">Trigger condition when sleep state change is detected</string>
    <string name="sensor_state">Sensor State</string>
    <string name="sensor_state_description">Light, orientation, vibration sensors</string>
    <string name="share_target_selection">Select Share Target</string>
    <string name="share_text">Share Text</string>
    <string name="share_text_description">Share text content through system share function</string>
    <string name="share_url_reminder_description">Suggest sharing URL when copying web links</string>
    <string name="share_url_reminder_status_disabled">Disabled</string>
    <string name="share_url_reminder_status_enabled">Monitoring clipboard</string>
    <string name="share_url_reminder_status_unconfigured">Feature ready to use</string>
    <string name="share_url_reminder_title">Share URL</string>
    <string name="shell_script">Shell Script</string>
    <string name="shell_script_placeholder">Enter shell script to execute</string>
    <string name="shell_script_placeholder_command">Enter shell command or script to execute</string>
    <string name="shell_script_settings">Shell Script Settings</string>
    <string name="shizuku_category_app_management">App Management Commands</string>
    <string name="shizuku_category_key_simulation">Key Simulation Commands</string>
    <string name="shizuku_category_network">Network Related Commands</string>
    <string name="shizuku_category_screen_operation">Screen Operation Commands</string>
    <string name="shizuku_category_system">System Control Commands</string>
    <string name="shizuku_install_guide">Please install and start Shizuku app first</string>
    <string name="shizuku_not_installed">Shizuku Not Installed</string>
    <string name="shizuku_not_running">Shizuku Not Running</string>
    <string name="shizuku_permission_required">Shizuku Permission Required</string>
    <string name="shopping_app_reminder_description">Suggest opening shopping app when copying product links</string>
    <string name="shopping_app_reminder_status_disabled">Disabled</string>
    <string name="shopping_app_reminder_status_enabled">Monitoring clipboard</string>
    <string name="shopping_app_reminder_status_unconfigured">Feature ready to use</string>
    <string name="shopping_app_reminder_title">Shopping App Reminder</string>
    <string name="shortcut_1_long_label">Quick Command 1</string>
    <string name="shortcut_1_short_label">Quick Command 1</string>
    <string name="shortcut_2_long_label">Quick Command 2</string>
    <string name="shortcut_2_short_label">Quick Command 2</string>
    <string name="shortcut_3_long_label">Quick Command 3</string>
    <string name="shortcut_3_short_label">Quick Command 3</string>
    <string name="shortcut_4_long_label">Quick Command 4</string>
    <string name="shortcut_4_short_label">Quick Command 4</string>
    <string name="shortcut_not_configured">This shortcut is not configured yet, please create a quick command in the app and select to use this static shortcut</string>
    <string name="sim_card_state">SIM Card State</string>
    <string name="sim_card_state_description">Triggered when SIM card state changes</string>
    <string name="smart_reminder_change_app">Change App</string>
    <string name="smart_reminder_config_error">Configuration Error</string>
    <string name="smart_reminder_config_not_found">Configuration item not found</string>
    <string name="smart_reminder_configure">Configure</string>
    <string name="smart_reminder_configured">Configured</string>
    <string name="smart_reminder_detail_config">Smart Reminder Detail Config</string>
    <string name="smart_reminder_detail_settings">Detail Settings</string>
    <string name="smart_reminder_needs_configuration">Configuration required</string>
    <string name="smart_reminder_ready_to_use">Ready to use</string>
    <string name="smart_reminder_select_app">Select App</string>
    <string name="smart_reminder_selected_apps_count">%d apps selected</string>
    <string name="smart_reminder_status_label">Status: </string>
    <string name="smart_reminder_tap_to_setup">Tap to setup</string>
    <string name="smart_reminder_type_not_found">Smart reminder type not found</string>
    <string name="smart_reminders_description">Intelligent detection and useful reminders</string>
    <string name="smart_reminders_title">Smart Reminders</string>
    <string name="state_change">State Change</string>
    <string name="state_change_description">App foreground, background state changes, background time exceeds threshold</string>
    <string name="stopwatch_selection">Select Stopwatch</string>
    <string name="storage_permission_description">Storage permission is required to export logs, please grant it in settings</string>
    <string name="storage_permission_required">Storage permission required to export logs</string>
    <string name="sun_event_sunrise">Sunrise</string>
    <string name="sun_event_sunset">Sunset</string>
    <string name="switch_operation_off">Off</string>
    <string name="switch_operation_on">On</string>
    <string name="switch_operation_toggle">Toggle</string>
    <string name="system_operation_accessibility_service_description">Used for system-level operations including: Quick Settings panel, Power menu, Recent tasks, App drawer, Accessibility feature toggle, Back button, Screen on/off control, Home button long press detection, Media key press detection, etc. Please enable this service if you need to use system operation or manual trigger related features.</string>
    <string name="system_operation_accessibility_service_name">Quick Commands - System Operation Service</string>
    <string name="system_setting_changed">System Setting</string>
    <string name="system_setting_changed_description">Triggered when system setting changes</string>
    <string name="system_settings">System Settings</string>
    <string name="system_settings_description">Modify key-value pairs in system settings table</string>
    <string name="tap_to_select_map_apps">Tap to select map apps to monitor</string>
    <string name="tap_to_select_music_apps">Tap to select music apps to monitor</string>
    <string name="task_alarm_reminder">Alarm Reminder</string>
    <string name="task_alarm_reminder_description">Set alarms and reminders</string>
    <string name="task_app_management">App Management</string>
    <string name="task_app_management_description">Launch, stop, install, uninstall apps</string>
    <string name="task_device_settings">Device Settings</string>
    <string name="task_device_settings_description">System settings, theme, wallpaper, color inversion and other device-level configurations</string>
    <string name="task_file_operation">File Operation</string>
    <string name="task_file_operation_description">Create, delete, move, copy files</string>
    <string name="task_location">Location Task</string>
    <string name="task_location_description">Get location information, geofencing</string>
    <string name="task_log">Log Task</string>
    <string name="task_log_description">Record log information</string>
    <string name="task_log_task">Log Task</string>
    <string name="task_log_task_description">Record log information</string>
    <string name="task_media_task">Media Task</string>
    <string name="task_media_task_description">Play audio, record audio, take photos, record videos</string>
    <string name="task_network">Network Task</string>
    <string name="task_network_description">HTTP requests, network detection</string>
    <string name="task_phone">Phone Task</string>
    <string name="task_phone_description">Make calls, answer calls, call log management</string>
    <string name="task_phone_task">Phone Task</string>
    <string name="task_phone_task_description">Make calls, answer calls, call log management</string>
    <string name="task_screen_control">Screen Control</string>
    <string name="task_screen_control_description">Brightness control, screen on/off, rotation control, etc.</string>
    <string name="task_screen_control_task">Screen Control</string>
    <string name="task_screen_control_task_description">Brightness control, screen on/off, rotation control, etc.</string>
    <string name="tasker_locale_plugin">Tasker/Locale Plugin</string>
    <string name="tasker_locale_plugin_description">Monitor Tasker/Locale plugin status</string>
    <string name="tasks_count">%1$d tasks</string>
    <string name="template_anti_embarrassment_mode_desc">Avoid embarrassment from loud volume when opening short video apps</string>
    <string name="template_anti_embarrassment_mode_title">Anti-Embarrassment Mode</string>
    <string name="template_auto_brightness_desc">Automatically adjust screen brightness based on ambient light</string>
    <string name="template_auto_brightness_title">Auto Brightness</string>
    <string name="template_battery_saver_desc">Automatically enable battery saver when battery level is below threshold</string>
    <string name="template_battery_saver_title">Battery Saver</string>
    <string name="template_category_automation">Automation</string>
    <string name="template_category_display">Display Control</string>
    <string name="template_category_network">Network Management</string>
    <string name="template_category_power">Power Management</string>
    <string name="template_category_system">System Operations</string>
    <string name="template_do_not_disturb_desc">Automatically enable do not disturb mode during specified time periods</string>
    <string name="template_do_not_disturb_title">Do Not Disturb</string>
    <string name="template_screen_off_network_desc">Turn off WiFi and mobile data after screen off with delay to save battery</string>
    <string name="template_screen_off_network_title">Screen Off Network</string>
    <string name="template_screen_on_network_desc">Restore previously saved network state after screen on with delay</string>
    <string name="template_screen_on_network_title">Screen On Network</string>
    <string name="text_content_label">Text Content</string>
    <string name="text_content_placeholder">e.g.: Login Successful, Loading Complete, etc.</string>
    <string name="time_based">Time Condition</string>
    <string name="time_based_description">Scheduled, periodic, delayed triggers</string>
    <string name="time_condition_delayed_trigger">Delayed Trigger</string>
    <string name="time_condition_delayed_trigger_description">Trigger once after specified delay</string>
    <string name="time_condition_periodic_time">Periodic Time</string>
    <string name="time_condition_periodic_time_description">Repeat trigger at specified time each week</string>
    <string name="time_condition_scheduled_time">Scheduled Time</string>
    <string name="time_condition_scheduled_time_description">Trigger at specified date and time</string>
    <string name="time_condition_stopwatch">Stopwatch</string>
    <string name="time_condition_stopwatch_description">Set countdown time, trigger when time reaches</string>
    <string name="time_condition_sun_event">Sun Event</string>
    <string name="time_condition_sun_event_description">Trigger at sunrise or sunset time</string>
    <string name="time_condition_time_period">Time Period</string>
    <string name="time_condition_time_period_description">Trigger within specified time period</string>
    <string name="toggle">Toggle</string>
    <string name="trigger_mode">Trigger Mode</string>
    <string name="unified_configuration">Unified Configuration Selection</string>
    <string name="update_now">Update Now</string>
    <string name="usage_stats_permission_description">This permission is required to detect app running status, please grant it in settings</string>
    <string name="usage_stats_permission_required">Usage Access Permission Required</string>
    <string name="volume_changed">Volume Changed</string>
    <string name="volume_changed_description">Triggered when system volume changes</string>
    <string name="volume_do_not_disturb">Do Not Disturb</string>
    <string name="volume_do_not_disturb_description">Set do not disturb mode status</string>
    <string name="volume_speakerphone_control">Speakerphone Control</string>
    <string name="volume_speakerphone_control_description">Control speakerphone enable, disable or toggle</string>
    <string name="volume_task">Volume Task</string>
    <string name="volume_task_description">Volume adjustment, mute control</string>
    <string name="volume_vibration_mode">Vibration Mode</string>
    <string name="volume_vibration_mode_description">Set device vibration mode</string>
    <string name="volume_volume_adjust">Volume Adjust</string>
    <string name="volume_volume_adjust_description">Increase or decrease volume for specified audio stream</string>
    <string name="volume_volume_change">Volume Change</string>
    <string name="volume_volume_change_description">Set volume level for specified audio stream</string>
    <string name="volume_volume_popup">Volume Popup</string>
    <string name="volume_volume_popup_description">Show system volume adjustment popup</string>
    <string name="widget_1_label">Quick Command 1</string>
    <string name="widget_2_label">Quick Command 2</string>
    <string name="widget_3_label">Quick Command 3</string>
    <string name="widget_4_label">Quick Command 4</string>
    <string name="widget_update_completed">Widget update completed</string>
    <string name="widget_update_enabled">Enable Widget Updates</string>
    <string name="widget_update_enabled_description">Periodically update desktop widget content, helps improve app survival rate but consumes more battery</string>
    <string name="widget_update_interval">Update Interval</string>
    <string name="widget_update_interval_error">Please enter a valid number (minimum value is 1)</string>
    <string name="widget_update_interval_hint">Recommended to set 6-24 hours, too frequent updates will affect battery life</string>
    <string name="widget_update_settings">Widget Update Settings</string>
</resources>
package com.weinuo.quickcommands.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.permission.PermissionRegistry
import com.weinuo.quickcommands.permission.GlobalPermissionManager

/**
 * 非展开式配置卡片
 *
 * 用于替代ExpandableConfigurationCard，点击后导航到新界面而不是展开内容。
 * 保持相同的权限检查逻辑，但移除展开/收起功能。
 *
 * @param T 操作类型的泛型参数
 * @param title 卡片标题
 * @param description 卡片描述
 * @param operationType 操作类型，用于权限检查
 * @param permissionRequired 是否需要权限检查，默认为true
 * @param onClick 点击回调，用于导航
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T : Any> NonExpandableConfigurationCard(
    title: String,
    description: String,
    operationType: T,
    permissionRequired: Boolean = true,
    onClick: () -> Unit
) {
    val context = LocalContext.current

    // 权限检查状态
    var selectedOperation by remember { mutableStateOf<T?>(null) }

    // 获取全局权限管理器
    val globalPermissionManager = remember { GlobalPermissionManager.getInstance(context) }

    // 权限检查结果
    val hasPermission = if (permissionRequired) {
        try {
            val permission = PermissionRegistry.hasPermissionForOperation(operationType, globalPermissionManager, context)
            android.util.Log.d("NonExpandableCard", "权限检查结果: operationType=$operationType, hasPermission=$permission")
            permission
        } catch (e: Exception) {
            android.util.Log.e("NonExpandableCard", "权限检查失败: operationType=$operationType", e)
            true // 权限检查失败时默认认为有权限，让用户能够进入配置界面
        }
    } else {
        android.util.Log.d("NonExpandableCard", "不需要权限检查: operationType=$operationType")
        true
    }

    // 权限检查 - 只在用户选择需要权限的操作时进行
    if (permissionRequired && selectedOperation != null) {
        val permissionStates by globalPermissionManager.permissionStates.collectAsState()

        com.weinuo.quickcommands.permission.PermissionAwareOperationSelector(
            selectedOperation = selectedOperation!!,
            onPermissionDialogDismissed = {
                // 权限对话框关闭时重置selectedOperation，确保下次点击能重新触发权限检查
                selectedOperation = null
            },
            context = context
        )

        // 权限检查完成后的处理逻辑 - 监听权限状态变化
        LaunchedEffect(selectedOperation, permissionStates) {
            if (selectedOperation != null) {
                // 检查当前操作是否需要权限以及权限是否已授予
                val hasRequiredPermissions = PermissionRegistry.hasPermissionForOperation(
                    operation = selectedOperation!!,
                    globalPermissionManager = globalPermissionManager,
                    context = context
                )

                if (hasRequiredPermissions) {
                    // 权限已授予，执行点击回调并重置权限检查状态
                    android.util.Log.d("NonExpandableCard", "权限已授予，执行点击回调")
                    onClick()
                    selectedOperation = null
                }
                // 如果权限未授予，保持selectedOperation状态，继续显示权限检查组件
            }
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp)
            .clickable(
                interactionSource = remember { androidx.compose.foundation.interaction.MutableInteractionSource() },
                indication = null // 移除涟漪效果，与old项目保持一致
            ) {
                android.util.Log.d("NonExpandableCard", "卡片被点击: title=$title, permissionRequired=$permissionRequired, hasPermission=$hasPermission")
                if (permissionRequired && !hasPermission) {
                    // 需要权限但没有权限，触发权限检查
                    android.util.Log.d("NonExpandableCard", "需要权限检查，设置selectedOperation")
                    selectedOperation = operationType
                } else {
                    // 有权限或不需要权限，直接执行点击回调
                    android.util.Log.d("NonExpandableCard", "直接执行点击回调")
                    onClick()
                }
            },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        // 卡片头部 - 标题、描述和箭头图标
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                // 权限状态提示
                if (permissionRequired && !hasPermission) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "需要权限",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }

            // 右侧箭头图标按钮
            IconButton(
                onClick = {
                    android.util.Log.d("NonExpandableCard", "箭头按钮被点击: title=$title")
                    if (permissionRequired && !hasPermission) {
                        // 需要权限但没有权限，触发权限检查
                        selectedOperation = operationType
                    } else {
                        // 有权限或不需要权限，直接执行点击回调
                        onClick()
                    }
                },
                interactionSource = remember { androidx.compose.foundation.interaction.MutableInteractionSource() },
                colors = IconButtonDefaults.iconButtonColors(
                    contentColor = MaterialTheme.colorScheme.onSurfaceVariant
                )
            ) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "进入配置"
                )
            }
        }
    }
}
